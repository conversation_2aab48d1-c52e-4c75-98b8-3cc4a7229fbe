{"name": "roblox project", "tree": {"$className": "DataModel", "Workspace": {"$path": "src/Workspace"}, "ReplicatedFirst": {"$path": "src/ReplicatedFirst"}, "ReplicatedStorage": {"$path": "src/ReplicatedStorage"}, "ServerScriptService": {"$path": "src/ServerScriptService"}, "ServerStorage": {"$path": "src/ServerStorage"}, "StarterGui": {"$path": "src/StarterGui"}, "StarterPack": {"$path": "src/StarterPack"}, "StarterPlayer": {"StarterCharacterScripts": {"$path": "src/StarterPlayer/StarterCharacterScripts"}, "StarterPlayerScripts": {"$path": "src/StarterPlayer/StarterPlayerScripts"}}}}