
local Players = game:GetService("Players")
local LocalPlayer = Players.LocalPlayer

local screenGui = Instance.new("ScreenGui")
screenGui.Name = "FPSBenchmarkGui"
screenGui.ResetOnSpawn = false
screenGui.Parent = LocalPlayer:WaitForChild("PlayerGui")

-- Main Frame (draggable)
local mainFrame = Instance.new("Frame")
mainFrame.Size = UDim2.new(0, 400, 0, 340)
mainFrame.Position = UDim2.new(0, 30, 1, -370)
mainFrame.AnchorPoint = Vector2.new(0, 1)
mainFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
mainFrame.BackgroundTransparency = 0.1
mainFrame.BorderSizePixel = 0
mainFrame.Active = true
mainFrame.Draggable = true
mainFrame.Parent = screenGui

-- Title
local title = Instance.new("TextLabel")
title.Size = UDim2.new(1, 0, 0, 40)
title.Position = UDim2.new(0, 0, 0, 0)
title.BackgroundTransparency = 1
title.Text = "Lag Test 2025 - Advanced"
title.TextColor3 = Color3.fromRGB(0,255,0)
title.Font = Enum.Font.GothamBold
title.TextScaled = true
title.Parent = mainFrame

-- Description
local desc = Instance.new("TextLabel")
desc.Size = UDim2.new(1, -20, 0, 30)
desc.Position = UDim2.new(0, 10, 0, 40)
desc.BackgroundTransparency = 1
desc.Text = "Spawn falling blocks to test your FPS!"
desc.TextColor3 = Color3.fromRGB(200,255,200)
desc.Font = Enum.Font.Gotham
desc.TextScaled = true
desc.TextWrapped = true
desc.Parent = mainFrame

-- Rain Rate Slider
local rainRateLabel = Instance.new("TextLabel")
rainRateLabel.Size = UDim2.new(0, 180, 0, 24)
rainRateLabel.Position = UDim2.new(0, 10, 0, 80)
rainRateLabel.BackgroundTransparency = 1
rainRateLabel.Text = "Rain Rate: 20 blocks/sec"
rainRateLabel.TextColor3 = Color3.fromRGB(255,255,255)
rainRateLabel.Font = Enum.Font.Gotham
desc.TextScaled = false
rainRateLabel.TextSize = 18
rainRateLabel.TextXAlignment = Enum.TextXAlignment.Left
rainRateLabel.Parent = mainFrame

local rainRateSlider = Instance.new("IntValue")
rainRateSlider.Name = "RainRate"
rainRateSlider.Value = 20
rainRateSlider.Parent = mainFrame

-- Max Blocks Slider
local maxBlocksLabel = Instance.new("TextLabel")
maxBlocksLabel.Size = UDim2.new(0, 180, 0, 24)
maxBlocksLabel.Position = UDim2.new(0, 10, 0, 110)
maxBlocksLabel.BackgroundTransparency = 1
maxBlocksLabel.Text = "Max Blocks: 1000"
maxBlocksLabel.TextColor3 = Color3.fromRGB(255,255,255)
maxBlocksLabel.Font = Enum.Font.Gotham
maxBlocksLabel.TextScaled = false
maxBlocksLabel.TextSize = 18
maxBlocksLabel.TextXAlignment = Enum.TextXAlignment.Left
maxBlocksLabel.Parent = mainFrame

local maxBlocksSlider = Instance.new("IntValue")
maxBlocksSlider.Name = "MaxBlocks"
maxBlocksSlider.Value = 1000
maxBlocksSlider.Parent = mainFrame

-- Block Size Slider
local blockSizeLabel = Instance.new("TextLabel")
blockSizeLabel.Size = UDim2.new(0, 180, 0, 24)
blockSizeLabel.Position = UDim2.new(0, 10, 0, 140)
blockSizeLabel.BackgroundTransparency = 1
blockSizeLabel.Text = "Block Size: 2"
blockSizeLabel.TextColor3 = Color3.fromRGB(255,255,255)
blockSizeLabel.Font = Enum.Font.Gotham
blockSizeLabel.TextScaled = false
blockSizeLabel.TextSize = 18
blockSizeLabel.TextXAlignment = Enum.TextXAlignment.Left
blockSizeLabel.Parent = mainFrame

local blockSizeSlider = Instance.new("IntValue")
blockSizeSlider.Name = "BlockSize"
blockSizeSlider.Value = 2
blockSizeSlider.Parent = mainFrame

-- Block Type Dropdown
local blockTypeLabel = Instance.new("TextLabel")
blockTypeLabel.Size = UDim2.new(0, 180, 0, 24)
blockTypeLabel.Position = UDim2.new(0, 10, 0, 170)
blockTypeLabel.BackgroundTransparency = 1
blockTypeLabel.Text = "Block Type: Cube"
blockTypeLabel.TextColor3 = Color3.fromRGB(255,255,255)
blockTypeLabel.Font = Enum.Font.Gotham
blockTypeLabel.TextScaled = false
blockTypeLabel.TextSize = 18
blockTypeLabel.TextXAlignment = Enum.TextXAlignment.Left
blockTypeLabel.Parent = mainFrame

local blockTypeValue = Instance.new("StringValue")
blockTypeValue.Name = "BlockType"
blockTypeValue.Value = "Cube"
blockTypeValue.Parent = mainFrame

-- Color Mode Toggle
local colorModeLabel = Instance.new("TextLabel")
colorModeLabel.Size = UDim2.new(0, 180, 0, 24)
colorModeLabel.Position = UDim2.new(0, 10, 0, 200)
colorModeLabel.BackgroundTransparency = 1
colorModeLabel.Text = "Color: Random"
colorModeLabel.TextColor3 = Color3.fromRGB(255,255,255)
colorModeLabel.Font = Enum.Font.Gotham
colorModeLabel.TextScaled = false
colorModeLabel.TextSize = 18
colorModeLabel.TextXAlignment = Enum.TextXAlignment.Left
colorModeLabel.Parent = mainFrame

local colorModeValue = Instance.new("BoolValue")
colorModeValue.Name = "RandomColor"
colorModeValue.Value = true
colorModeValue.Parent = mainFrame

-- Start, Stop, Reset Buttons
local startButton = Instance.new("TextButton")
startButton.Size = UDim2.new(0, 110, 0, 40)
startButton.Position = UDim2.new(0, 10, 1, -50)
startButton.AnchorPoint = Vector2.new(0, 1)
startButton.BackgroundColor3 = Color3.fromRGB(30, 180, 30)
startButton.TextColor3 = Color3.fromRGB(255,255,255)
startButton.TextScaled = true
startButton.Font = Enum.Font.GothamBold
startButton.Text = "Start"
startButton.AutoButtonColor = true
startButton.BorderSizePixel = 0
startButton.Parent = mainFrame

local stopButton = Instance.new("TextButton")
stopButton.Size = UDim2.new(0, 110, 0, 40)
stopButton.Position = UDim2.new(0, 130, 1, -50)
stopButton.AnchorPoint = Vector2.new(0, 1)
stopButton.BackgroundColor3 = Color3.fromRGB(200, 180, 30)
stopButton.TextColor3 = Color3.fromRGB(255,255,255)
stopButton.TextScaled = true
stopButton.Font = Enum.Font.GothamBold
stopButton.Text = "Stop"
stopButton.AutoButtonColor = true
stopButton.BorderSizePixel = 0
stopButton.Visible = false
stopButton.Parent = mainFrame

local resetButton = Instance.new("TextButton")
resetButton.Size = UDim2.new(0, 110, 0, 40)
resetButton.Position = UDim2.new(0, 250, 1, -50)
resetButton.AnchorPoint = Vector2.new(0, 1)
resetButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
resetButton.TextColor3 = Color3.fromRGB(255,255,255)
resetButton.TextScaled = true
resetButton.Font = Enum.Font.GothamBold
resetButton.Text = "Reset"
resetButton.AutoButtonColor = true
resetButton.BorderSizePixel = 0
resetButton.Visible = false
resetButton.Parent = mainFrame

return {
    MainFrame = mainFrame,
    StartButton = startButton,
    StopButton = stopButton,
    ResetButton = resetButton,
    RainRate = rainRateSlider,
    MaxBlocks = maxBlocksSlider,
    BlockSize = blockSizeSlider,
    BlockType = blockTypeValue,
    RandomColor = colorModeValue,
    RainRateLabel = rainRateLabel,
    MaxBlocksLabel = maxBlocksLabel,
    BlockSizeLabel = blockSizeLabel,
    BlockTypeLabel = blockTypeLabel,
    ColorModeLabel = colorModeLabel
}
