--[[
	Benchmark Configuration Module
	
	Contains all configuration constants and settings for the FPS benchmark system.
]]

local BenchmarkConfig = {}

-- FPS Performance Thresholds
BenchmarkConfig.FPS_THRESHOLDS = {
	EXCELLENT = 60,
	GOOD = 45,
	FAIR = 30,
	POOR = 15
}

-- Spawn Rate Options (objects per second)
BenchmarkConfig.SPAWN_RATES = {
	SLOW = 1,      -- 1 object per second
	MEDIUM = 3,    -- 3 objects per second  
	FAST = 5,      -- 5 objects per second
	EXTREME = 10   -- 10 objects per second
}

-- Object Limits
BenchmarkConfig.MAX_OBJECTS_PER_TEST = 1000
BenchmarkConfig.ABSOLUTE_MAX_OBJECTS = 5000

-- Performance Monitoring
BenchmarkConfig.PERFORMANCE_SAMPLE_RATE = 0.1  -- Sample every 0.1 seconds
BenchmarkConfig.FPS_HISTORY_SIZE = 30          -- Keep last 30 FPS samples for smoothing
BenchmarkConfig.EMERGENCY_STOP_FPS = 5         -- Emergency stop if FPS drops below this

-- UI Configuration
BenchmarkConfig.UI = {
	MAIN_FRAME_SIZE = {400, 500},
	CORNER_RADIUS = 10,
	STROKE_THICKNESS = 2,
	
	COLORS = {
		BACKGROUND = Color3.fromRGB(40, 40, 40),
		SECONDARY_BACKGROUND = Color3.fromRGB(50, 50, 50),
		BORDER = Color3.fromRGB(80, 80, 80),
		TEXT_PRIMARY = Color3.fromRGB(255, 255, 255),
		TEXT_SECONDARY = Color3.fromRGB(200, 200, 200),
		
		-- Button Colors
		BUTTON_PRIMARY = Color3.fromRGB(70, 130, 180),
		BUTTON_SUCCESS = Color3.fromRGB(34, 139, 34),
		BUTTON_DANGER = Color3.fromRGB(220, 20, 60),
		BUTTON_WARNING = Color3.fromRGB(255, 165, 0),
		BUTTON_SECONDARY = Color3.fromRGB(128, 128, 128),
		BUTTON_DISABLED = Color3.fromRGB(70, 70, 70),
		
		-- Performance Rating Colors
		EXCELLENT = Color3.fromRGB(0, 255, 0),
		GOOD = Color3.fromRGB(255, 255, 0),
		FAIR = Color3.fromRGB(255, 165, 0),
		POOR = Color3.fromRGB(255, 0, 0)
	}
}

-- Test Configuration
BenchmarkConfig.TESTS = {
	GEOMETRY = {
		name = "Geometry Test",
		description = "Tests basic part rendering performance",
		shapes = {"Block", "Ball", "Cylinder", "Wedge"},
		materials = {
			Enum.Material.Plastic,
			Enum.Material.Wood,
			Enum.Material.Metal,
			Enum.Material.Concrete
		},
		colors = {
			Color3.fromRGB(255, 0, 0),
			Color3.fromRGB(0, 255, 0),
			Color3.fromRGB(0, 0, 255),
			Color3.fromRGB(255, 255, 0),
			Color3.fromRGB(255, 0, 255),
			Color3.fromRGB(0, 255, 255)
		},
		sizeRange = {min = 1, max = 4},
		spawnArea = {x = {-50, 50}, y = {10, 100}, z = {-50, 50}}
	},
	
	LIGHTING = {
		name = "Lighting Test",
		description = "Tests lighting and particle effects performance",
		effectTypes = {"PointLight", "SpotLight", "ParticleEmitter"},
		spawnArea = {x = {-30, 30}, y = {5, 50}, z = {-30, 30}},
		lightSettings = {
			brightnessRange = {1, 3},
			rangeMin = 10,
			rangeMax = 30,
			spotAngleRange = {30, 120}
		},
		particleSettings = {
			rateRange = {10, 50},
			lifetimeRange = {1, 3},
			speedRange = {5, 15}
		}
	},
	
	PHYSICS = {
		name = "Physics Test", 
		description = "Tests physics simulation performance",
		spawnArea = {x = {-40, 40}, y = {20, 80}, z = {-40, 40}},
		sizeRange = {min = 1, max = 3},
		velocityRange = {x = {-20, 20}, y = {-10, 10}, z = {-20, 20}},
		forceStrength = 4000
	},
	
	RENDERING = {
		name = "Rendering Test",
		description = "Tests complex material and texture rendering",
		spawnArea = {x = {-60, 60}, y = {5, 60}, z = {-60, 60}},
		sizeRange = {min = 2, max = 6},
		materials = {
			Enum.Material.ForceField,
			Enum.Material.Glass,
			Enum.Material.Neon,
			Enum.Material.Metal,
			Enum.Material.DiamondPlate
		},
		transparencyRange = {0, 0.7},
		reflectanceRange = {0, 0.5},
		meshTypes = {
			Enum.MeshType.Sphere,
			Enum.MeshType.Cylinder,
			Enum.MeshType.Wedge
		},
		scaleRange = {0.5, 1.5},
		decalChance = 0.33  -- 33% chance to add decal
	}
}

-- Scoring System
BenchmarkConfig.SCORING = {
	-- Points awarded based on objects maintained at different FPS thresholds
	POINTS_PER_OBJECT_60FPS = 10,
	POINTS_PER_OBJECT_30FPS = 5,
	
	-- Performance ratings based on total score
	RATING_THRESHOLDS = {
		EXCELLENT = 5000,
		GOOD = 3000,
		FAIR = 1500,
		POOR = 0
	}
}

-- Safety Limits
BenchmarkConfig.SAFETY = {
	MAX_MEMORY_MB = 2048,        -- Stop if memory usage exceeds this
	MAX_TEST_DURATION = 300,     -- Stop test after 5 minutes
	CLEANUP_BATCH_SIZE = 50,     -- Clean up objects in batches
	CLEANUP_DELAY = 0.1          -- Delay between cleanup batches
}

return BenchmarkConfig
