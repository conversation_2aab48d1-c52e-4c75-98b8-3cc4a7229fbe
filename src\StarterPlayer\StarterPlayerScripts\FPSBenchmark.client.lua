

local RunService = game:GetService("RunService")
local Players = game:GetService("Players")
local LocalPlayer = Players.LocalPlayer

-- UI
local uiModule = nil
local UI = {}
local success, err = pcall(function()
    uiModule = require(game:GetService("StarterGui"):WaitForChild("FPSBenchmarkUI"))
    UI = uiModule
end)
if not success then warn("FPSBenchmarkUI load error:", err) end

-- Show/Hide GUI button (top right)
local toggleButton = Instance.new("TextButton")
toggleButton.Size = UDim2.new(0, 60, 0, 32)
toggleButton.Position = UDim2.new(1, -70, 0, 10)
toggleButton.AnchorPoint = Vector2.new(0, 0)
toggleButton.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
toggleButton.TextColor3 = Color3.fromRGB(255,255,255)
toggleButton.TextScaled = true
toggleButton.Font = Enum.Font.GothamBold
toggleButton.Text = "UI"
toggleButton.AutoButtonColor = true
toggleButton.BorderSizePixel = 0
toggleButton.Parent = game:GetService("Players").LocalPlayer:WaitForChild("PlayerGui")

-- FPS display
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "FPSBenchmarkFPSGui"
screenGui.ResetOnSpawn = false
screenGui.Parent = LocalPlayer:WaitForChild("PlayerGui")

local fpsLabel = Instance.new("TextLabel")
fpsLabel.Size = UDim2.new(0, 220, 0, 48)
fpsLabel.Position = UDim2.new(0, 10, 0, 10)
fpsLabel.BackgroundTransparency = 0.3
fpsLabel.BackgroundColor3 = Color3.fromRGB(0,0,0)
fpsLabel.TextColor3 = Color3.fromRGB(0,255,0)
fpsLabel.TextStrokeTransparency = 0.7
fpsLabel.TextScaled = true
fpsLabel.Font = Enum.Font.GothamBold
fpsLabel.Text = "FPS: ..."
fpsLabel.Visible = false
fpsLabel.Parent = screenGui

-- Rain config defaults
local DEFAULTS = {
    RainRate = 20,
    MaxBlocks = 1000,
    BlockSize = 2,
    BlockType = "Cube",
    RandomColor = true
}

local raining = false
local parts = {}
local partCount = 0
local paused = false

-- FPS calculation
local frameTimes = {}
local lastUpdate = 0

RunService.RenderStepped:Connect(function(dt)
    if not raining then return end
    table.insert(frameTimes, tick())
    while frameTimes[1] and tick() - frameTimes[1] > 1 do
        table.remove(frameTimes, 1)
    end
    if tick() - lastUpdate > 0.1 then
        fpsLabel.Text = string.format("FPS: %d | Parts: %d", #frameTimes, partCount)
        lastUpdate = tick()
    end
end)

-- Helper: get current UI values
local function getSetting(name, fallback)
    if UI and UI[name] then
        local v = UI[name]
        if v:IsA("IntValue") or v:IsA("NumberValue") then return v.Value end
        if v:IsA("StringValue") then return v.Value end
        if v:IsA("BoolValue") then return v.Value end
    end
    return fallback
end

-- Helper: get block type
local function createBlock(blockType, size, color)
    local part
    if blockType == "Cube" then
        part = Instance.new("Part")
        part.Shape = Enum.PartType.Block
        part.Size = Vector3.new(size, size, size)
    elseif blockType == "Sphere" then
        part = Instance.new("Part")
        part.Shape = Enum.PartType.Ball
        part.Size = Vector3.new(size, size, size)
    elseif blockType == "Wedge" then
        part = Instance.new("WedgePart")
        part.Size = Vector3.new(size, size, size)
    else
        part = Instance.new("Part")
        part.Shape = Enum.PartType.Block
        part.Size = Vector3.new(size, size, size)
    end
    part.Anchored = false
    part.CanCollide = true
    part.BrickColor = color
    part.Name = "FPSRainBlock"
    part:SetAttribute("ClientOnly", true)
    return part
end

-- Rain logic
local rainConnection
local function getRainCenter()
    local char = LocalPlayer.Character
    if char and char:FindFirstChild("HumanoidRootPart") then
        return char.HumanoidRootPart.Position
    end
    return Vector3.new(0,0,0)
end

local function startRain()
    raining = true
    paused = false
    partCount = 0
    fpsLabel.Visible = true
    if UI.ResetButton then UI.ResetButton.Visible = true end
    if UI.StopButton then UI.StopButton.Visible = true end
    if UI.StartButton then UI.StartButton.Visible = false end
    rainConnection = RunService.Heartbeat:Connect(function(dt)
        if paused then return end
        local rainRate = getSetting("RainRate", DEFAULTS.RainRate)
        local maxBlocks = getSetting("MaxBlocks", DEFAULTS.MaxBlocks)
        local blockSize = getSetting("BlockSize", DEFAULTS.BlockSize)
        local blockType = getSetting("BlockType", DEFAULTS.BlockType)
        local randomColor = getSetting("RandomColor", DEFAULTS.RandomColor)
        local center = getRainCenter()
        for i = 1, math.max(1, math.floor(rainRate * dt)) do
            if partCount >= maxBlocks then return end
            -- Avoid spawning inside player
            local pos
            local tries = 0
            repeat
                pos = Vector3.new(
                    center.X + math.random(-100, 100),
                    center.Y + 100,
                    center.Z + math.random(-100, 100)
                )
                tries = tries + 1
            until (not (LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") and (pos - LocalPlayer.Character.HumanoidRootPart.Position).magnitude < 5)) or tries > 5
            local color = randomColor and BrickColor.Random() or BrickColor.new("Bright blue")
            local part = createBlock(blockType, blockSize, color)
            part.Position = pos
            part.Parent = workspace
            table.insert(parts, part)
            partCount = partCount + 1
        end
    end)
end

local function stopRain()
    paused = true
    if UI.StopButton then UI.StopButton.Visible = false end
    if UI.StartButton then UI.StartButton.Visible = true end
end

local function resumeRain()
    paused = false
    if UI.StopButton then UI.StopButton.Visible = true end
    if UI.StartButton then UI.StartButton.Visible = false end
end

local function stopRainAndCleanup()
    raining = false
    paused = false
    if rainConnection then rainConnection:Disconnect() end
    for _, part in ipairs(parts) do
        if part and part.Parent then part:Destroy() end
    end
    parts = {}
    partCount = 0
    fpsLabel.Visible = false
    if UI.ResetButton then UI.ResetButton.Visible = false end
    if UI.StopButton then UI.StopButton.Visible = false end
    if UI.StartButton then UI.StartButton.Visible = true end
end

-- UI logic
if UI.StartButton then
    UI.StartButton.Visible = true
    UI.StartButton.MouseButton1Click:Connect(function()
        if raining then return end
        startRain()
    end)
end
if UI.StopButton then
    UI.StopButton.MouseButton1Click:Connect(function()
        if raining then stopRain() end
    end)
end
if UI.ResetButton then
    UI.ResetButton.MouseButton1Click:Connect(function()
        stopRainAndCleanup()
    end)
end

-- Show/Hide GUI logic
if UI.MainFrame then
    toggleButton.MouseButton1Click:Connect(function()
        UI.MainFrame.Visible = not UI.MainFrame.Visible
    end)
end

-- Ray Tracing Button
if UI.MainFrame then
    local rayButton = Instance.new("TextButton")
    rayButton.Size = UDim2.new(0, 120, 0, 36)
    rayButton.Position = UDim2.new(1, -130, 0, 10)
    rayButton.AnchorPoint = Vector2.new(0, 0)
    rayButton.BackgroundColor3 = Color3.fromRGB(60, 60, 200)
    rayButton.TextColor3 = Color3.fromRGB(255,255,255)
    rayButton.TextScaled = true
    rayButton.Font = Enum.Font.GothamBold
    rayButton.Text = "Ray Tracing"
    rayButton.AutoButtonColor = true
    rayButton.BorderSizePixel = 0
    rayButton.Parent = UI.MainFrame
    rayButton.MouseButton1Click:Connect(function()
        -- PLACEHOLDER: Insert your ray tracing plugin/script logic here
        print("[LagTest] Ray tracing triggered!")
        -- Example: require(game.ReplicatedStorage:WaitForChild("YourRayTracingModule")).Start()
    end)
end

-- Cleanup on player removing
LocalPlayer.AncestryChanged:Connect(function(_, parent)
    if not parent then
        stopRainAndCleanup()
    end
end)
