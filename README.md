# Roblox FPS Benchmarking System

A comprehensive client-side performance testing tool for Roblox that measures frame rate impact by progressively spawning objects across different stress test categories.

## Features

### Core Functionality
- **Real-time FPS monitoring** using RunService.Heartbeat
- **Memory usage tracking** via Stats service
- **Network performance monitoring** (ping/latency)
- **Device detection** (Mobile/Desktop/Tablet)
- **Emergency stop mechanisms** to prevent client crashes

### Benchmark Test Categories

1. **Geometry Stress Test**
   - Spawns basic parts (cubes, spheres, cylinders, wedges)
   - Tests fundamental rendering performance
   - Varies materials, colors, and sizes

2. **Lighting/Effects Stress Test**
   - Spawns PointLights, SpotLights, and ParticleEmitters
   - Tests lighting system and particle effects performance
   - Configurable brightness, range, and particle settings

3. **Physics Stress Test**
   - Spawns parts with CanCollide=true and BodyVelocity
   - Tests physics simulation performance
   - Applies random forces and velocities

4. **Rendering Stress Test**
   - Spawns parts with complex materials and transparency
   - Tests advanced rendering features
   - Includes textures, reflectance, and special meshes

### User Interface
- **Modern responsive design** with UICorner and UIStroke
- **Real-time performance metrics** display
- **Progress tracking** for each test category
- **Performance rating system** (Excellent/Good/Fair/Poor)
- **Results visualization** with detailed statistics
- **Device information** display

## Installation

### Method 1: Argon Sync (Recommended)
If you're using Argon for Roblox Studio sync:

1. Clone or download this repository
2. Ensure Argon is running and connected to your Roblox Studio
3. The files will automatically sync to your place

### Method 2: Manual Installation

1. **Create the folder structure in Roblox Studio:**
   ```
   ReplicatedStorage/
   └── BenchmarkModules/
       ├── BenchmarkConfig.lua
       ├── PerformanceMonitor.lua
       ├── BenchmarkTests.lua
       └── BenchmarkUI.lua
   
   StarterPlayer/
   └── StarterPlayerScripts/
       └── FPSBenchmarkMain.lua
   ```

2. **Copy the module scripts:**
   - Copy each `.lua` file from `src/ReplicatedStorage/BenchmarkModules/` to the corresponding ModuleScript in Studio
   - Copy `src/StarterPlayer/StarterPlayerScripts/FPSBenchmarkMain.lua` to a LocalScript in StarterPlayerScripts

3. **Ensure proper script types:**
   - `FPSBenchmarkMain.lua` should be a **LocalScript**
   - All files in `BenchmarkModules` should be **ModuleScripts**

## Usage

### Starting the Benchmark

1. **Join the game** - The benchmark UI will automatically appear
2. **Select a test category** by clicking one of the four test buttons:
   - Geometry Test
   - Lighting Test
   - Physics Test
   - Rendering Test
3. **Monitor performance** in real-time via the performance display panel

### Understanding the Results

The system tracks three key metrics for each test:
- **Objects at 60 FPS**: Maximum objects spawned while maintaining 60+ FPS
- **Objects at 30 FPS**: Maximum objects spawned while maintaining 30+ FPS  
- **Max Objects**: Total objects spawned before test completion or emergency stop

### Performance Ratings
- **Excellent**: 60+ FPS (Green)
- **Good**: 45-59 FPS (Yellow)
- **Fair**: 30-44 FPS (Orange)
- **Poor**: Below 30 FPS (Red)

### Controls
- **Stop**: Immediately halt the current test
- **Reset**: Clear all test data and spawned objects
- **Results**: View detailed benchmark results

## Configuration

### Customizing Test Parameters

Edit `BenchmarkConfig.lua` to modify:

```lua
-- Spawn rates (objects per second)
SPAWN_RATES = {
    SLOW = 1,
    MEDIUM = 3,
    FAST = 5,
    EXTREME = 10
}

-- Object limits
MAX_OBJECTS_PER_TEST = 1000

-- FPS thresholds
FPS_THRESHOLDS = {
    EXCELLENT = 60,
    GOOD = 45,
    FAIR = 30,
    POOR = 15
}
```

### Safety Limits

The system includes built-in safety mechanisms:
- **Emergency stop** at 5 FPS to prevent crashes
- **Memory limit** monitoring
- **Maximum test duration** (5 minutes)
- **Batch cleanup** to prevent lag during object removal

## Technical Details

### Architecture
- **Modular design** with separate concerns
- **Event-driven updates** using RunService.Heartbeat
- **Smooth FPS calculation** using rolling average
- **Efficient object cleanup** with batched destruction

### Performance Monitoring
- FPS calculated from delta time with 30-sample smoothing
- Memory usage sampled every 0.1 seconds
- Network statistics when available
- Device capability detection

### Compatibility
- Works on all Roblox platforms (PC, Mobile, Console)
- Automatically adjusts for device capabilities
- No external dependencies required

## Troubleshooting

### Common Issues

**UI doesn't appear:**
- Ensure `FPSBenchmarkMain.lua` is a LocalScript in StarterPlayerScripts
- Check that all ModuleScripts are properly placed in ReplicatedStorage

**Tests don't start:**
- Verify all module dependencies are correctly placed
- Check the Developer Console (F9) for error messages

**Performance seems inaccurate:**
- FPS calculations use smoothing - wait a few seconds for stabilization
- Ensure no other intensive scripts are running simultaneously

### Debug Information

The system exports debug globals:
```lua
_G.BenchmarkManager -- Access to benchmark manager
_G.BenchmarkUI      -- Access to UI controller
```

Use these in the Developer Console for debugging.

## Contributing

Feel free to submit issues and enhancement requests! When contributing:

1. Maintain the modular architecture
2. Follow existing code style and commenting
3. Test on multiple device types
4. Update documentation for new features

## License

This project is open source and available under the MIT License.
