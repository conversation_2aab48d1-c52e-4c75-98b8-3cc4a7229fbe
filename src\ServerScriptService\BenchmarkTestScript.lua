--[[
	Benchmark Test Script (Server)
	
	Optional server-side script to verify the benchmark system is working correctly.
	This script can be used to test the installation and provide debug information.
	
	Place this as a regular Script in ServerScriptService (optional).
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for benchmark modules to load
local benchmarkModules = ReplicatedStorage:WaitForChild("BenchmarkModules", 10)

if not benchmarkModules then
	warn("BenchmarkModules folder not found in ReplicatedStorage!")
	warn("Please ensure the benchmark system is properly installed.")
	return
end

-- Check for required modules
local requiredModules = {"BenchmarkConfig", "PerformanceMonitor", "BenchmarkTests", "BenchmarkUI"}
local missingModules = {}

for _, moduleName in ipairs(requiredModules) do
	local module = benchmarkModules:FindFirstChild(moduleName)
	if not module then
		table.insert(missingModules, moduleName)
	elseif not module:IsA("ModuleScript") then
		warn(moduleName .. " exists but is not a ModuleScript!")
	end
end

if #missingModules > 0 then
	warn("Missing benchmark modules:", table.concat(missingModules, ", "))
	warn("Please check the installation guide.")
else
	print("✓ All benchmark modules found and properly configured")
end

-- Test module loading
local function testModuleLoading()
	local success, config = pcall(function()
		return require(benchmarkModules.BenchmarkConfig)
	end)
	
	if success then
		print("✓ BenchmarkConfig loaded successfully")
		print("  - FPS Thresholds:", config.FPS_THRESHOLDS.EXCELLENT, config.FPS_THRESHOLDS.GOOD, config.FPS_THRESHOLDS.FAIR, config.FPS_THRESHOLDS.POOR)
		print("  - Max Objects Per Test:", config.MAX_OBJECTS_PER_TEST)
	else
		warn("✗ Failed to load BenchmarkConfig:", config)
	end
	
	local success2, tests = pcall(function()
		return require(benchmarkModules.BenchmarkTests)
	end)
	
	if success2 then
		print("✓ BenchmarkTests loaded successfully")
		print("  - Available tests: GeometryTest, LightingTest, PhysicsTest, RenderingTest")
	else
		warn("✗ Failed to load BenchmarkTests:", tests)
	end
end

-- Monitor player connections and provide feedback
local function onPlayerAdded(player)
	print("Player joined:", player.Name)
	
	-- Wait a bit for the client scripts to load
	wait(3)
	
	-- Check if the benchmark UI should be visible
	local playerGui = player:WaitForChild("PlayerGui", 5)
	if playerGui then
		local benchmarkUI = playerGui:FindFirstChild("FPSBenchmarkUI")
		if benchmarkUI then
			print("✓ Benchmark UI created for", player.Name)
		else
			warn("✗ Benchmark UI not found for", player.Name)
			warn("  Check that FPSBenchmarkMain.lua is a LocalScript in StarterPlayerScripts")
		end
	end
end

-- Monitor workspace for benchmark objects
local function monitorBenchmarkObjects()
	local lastObjectCount = 0
	
	RunService.Heartbeat:Connect(function()
		local objectCount = 0
		
		-- Count benchmark test objects
		for _, obj in ipairs(workspace:GetChildren()) do
			if string.find(obj.Name, "Test_") then
				objectCount = objectCount + 1
			end
		end
		
		-- Report significant changes
		if objectCount ~= lastObjectCount and objectCount > 0 then
			if objectCount > lastObjectCount then
				print("Benchmark objects spawned:", objectCount, "(+" .. (objectCount - lastObjectCount) .. ")")
			else
				print("Benchmark objects cleaned up:", objectCount, "(-" .. (lastObjectCount - objectCount) .. ")")
			end
			lastObjectCount = objectCount
		elseif objectCount == 0 and lastObjectCount > 0 then
			print("✓ All benchmark objects cleaned up")
			lastObjectCount = 0
		end
	end)
end

-- Provide server commands for testing
local function setupServerCommands()
	-- Command to check system status
	game.Players.PlayerAdded:Connect(function(player)
		player.Chatted:Connect(function(message)
			if player.Name == game.CreatorId or player.Name == "YourUsernameHere" then -- Replace with your username
				if message:lower() == "/benchmark status" then
					print("=== Benchmark System Status ===")
					print("Modules found:", benchmarkModules and "Yes" or "No")
					print("Required modules:", #missingModules == 0 and "All present" or "Missing: " .. table.concat(missingModules, ", "))
					
					local objectCount = 0
					for _, obj in ipairs(workspace:GetChildren()) do
						if string.find(obj.Name, "Test_") then
							objectCount = objectCount + 1
						end
					end
					print("Active benchmark objects:", objectCount)
					
					local playerCount = #game.Players:GetPlayers()
					print("Players in game:", playerCount)
				elseif message:lower() == "/benchmark cleanup" then
					-- Emergency cleanup command
					local cleaned = 0
					for _, obj in ipairs(workspace:GetChildren()) do
						if string.find(obj.Name, "Test_") then
							obj:Destroy()
							cleaned = cleaned + 1
						end
					end
					print("Emergency cleanup completed. Removed", cleaned, "benchmark objects.")
				end
			end
		end)
	end)
end

-- Initialize monitoring
print("=== FPS Benchmark System - Server Monitor ===")
print("Checking installation...")

testModuleLoading()
monitorBenchmarkObjects()
setupServerCommands()

Players.PlayerAdded:Connect(onPlayerAdded)

-- Handle players already in game
for _, player in ipairs(Players:GetPlayers()) do
	onPlayerAdded(player)
end

print("✓ Benchmark monitoring active")
print("Available commands (for game owner):")
print("  /benchmark status - Check system status")
print("  /benchmark cleanup - Emergency cleanup of benchmark objects")
