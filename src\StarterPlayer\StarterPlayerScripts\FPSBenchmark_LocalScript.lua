--[[
	Roblox FPS Benchmarking System
	Main LocalScript - Place in StarterPlayer > StarterPlayerScripts
	
	This script provides comprehensive client-side performance testing
	by progressively spawning objects and monitoring frame rate impact.
]]

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local Stats = game:GetService("Stats")
local TweenService = game:GetService("TweenService")
local HttpService = game:GetService("HttpService")

-- Player and GUI setup
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Core Benchmark Manager
local BenchmarkManager = {}
BenchmarkManager.__index = BenchmarkManager

-- Configuration
local CONFIG = {
	FPS_THRESHOLDS = {
		EXCELLENT = 60,
		GOOD = 45,
		FAIR = 30,
		POOR = 15
	},
	SPAWN_RATES = {
		SLOW = 1,    -- 1 object per second
		MEDIUM = 3,  -- 3 objects per second
		FAST = 5,    -- 5 objects per second
		EXTREME = 10 -- 10 objects per second
	},
	MAX_OBJECTS_PER_TEST = 1000,
	PERFORMANCE_SAMPLE_RATE = 0.1, -- Sample every 0.1 seconds
	EMERGENCY_STOP_FPS = 5 -- Emergency stop if FPS drops below this
}

-- Performance Monitoring
local PerformanceMonitor = {}
PerformanceMonitor.__index = PerformanceMonitor

function PerformanceMonitor.new()
	local self = setmetatable({}, PerformanceMonitor)
	
	self.frameCount = 0
	self.lastTime = tick()
	self.currentFPS = 60
	self.fpsHistory = {}
	self.memoryUsage = 0
	self.ping = 0
	
	-- Connect to heartbeat for FPS monitoring
	self.heartbeatConnection = RunService.Heartbeat:Connect(function(deltaTime)
		self:updateFPS(deltaTime)
	end)
	
	-- Performance sampling timer
	self.sampleConnection = RunService.Heartbeat:Connect(function()
		if tick() - self.lastSampleTime >= CONFIG.PERFORMANCE_SAMPLE_RATE then
			self:samplePerformance()
			self.lastSampleTime = tick()
		end
	end)
	
	self.lastSampleTime = tick()
	
	return self
end

function PerformanceMonitor:updateFPS(deltaTime)
	-- Calculate FPS using delta time
	if deltaTime > 0 then
		self.currentFPS = 1 / deltaTime
		
		-- Smooth FPS calculation
		table.insert(self.fpsHistory, self.currentFPS)
		if #self.fpsHistory > 30 then -- Keep last 30 samples
			table.remove(self.fpsHistory, 1)
		end
		
		-- Calculate average FPS
		local sum = 0
		for _, fps in ipairs(self.fpsHistory) do
			sum = sum + fps
		end
		self.currentFPS = sum / #self.fpsHistory
	end
end

function PerformanceMonitor:samplePerformance()
	-- Memory usage
	local memStats = Stats:FindFirstChild("MemoryUsage")
	if memStats then
		self.memoryUsage = memStats.Value
	end
	
	-- Network stats
	local networkStats = Stats:FindFirstChild("Network")
	if networkStats and networkStats:FindFirstChild("ServerStatsItem") then
		local serverStats = networkStats.ServerStatsItem
		if serverStats:FindFirstChild("Data Ping") then
			self.ping = serverStats["Data Ping"].Value
		end
	end
end

function PerformanceMonitor:getCurrentFPS()
	return math.floor(self.currentFPS * 10) / 10 -- Round to 1 decimal
end

function PerformanceMonitor:getMemoryUsage()
	return self.memoryUsage
end

function PerformanceMonitor:getPing()
	return self.ping
end

function PerformanceMonitor:getPerformanceRating()
	local fps = self:getCurrentFPS()
	if fps >= CONFIG.FPS_THRESHOLDS.EXCELLENT then
		return "Excellent", Color3.fromRGB(0, 255, 0)
	elseif fps >= CONFIG.FPS_THRESHOLDS.GOOD then
		return "Good", Color3.fromRGB(255, 255, 0)
	elseif fps >= CONFIG.FPS_THRESHOLDS.FAIR then
		return "Fair", Color3.fromRGB(255, 165, 0)
	else
		return "Poor", Color3.fromRGB(255, 0, 0)
	end
end

function PerformanceMonitor:destroy()
	if self.heartbeatConnection then
		self.heartbeatConnection:Disconnect()
	end
	if self.sampleConnection then
		self.sampleConnection:Disconnect()
	end
end

-- Benchmark Test Modules
local BenchmarkTests = {}

-- Geometry Stress Test
BenchmarkTests.GeometryTest = {}
BenchmarkTests.GeometryTest.__index = BenchmarkTests.GeometryTest

function BenchmarkTests.GeometryTest.new()
	local self = setmetatable({}, BenchmarkTests.GeometryTest)
	self.spawnedObjects = {}
	self.objectCount = 0
	return self
end

function BenchmarkTests.GeometryTest:spawnObject()
	local shapes = {"Block", "Ball", "Cylinder", "Wedge"}
	local materials = {Enum.Material.Plastic, Enum.Material.Wood, Enum.Material.Metal, Enum.Material.Concrete}
	local colors = {
		Color3.fromRGB(255, 0, 0),
		Color3.fromRGB(0, 255, 0),
		Color3.fromRGB(0, 0, 255),
		Color3.fromRGB(255, 255, 0),
		Color3.fromRGB(255, 0, 255),
		Color3.fromRGB(0, 255, 255)
	}
	
	local part = Instance.new("Part")
	part.Name = "GeometryTest_" .. self.objectCount
	part.Shape = Enum.PartType[shapes[math.random(1, #shapes)]]
	part.Material = materials[math.random(1, #materials)]
	part.Color = colors[math.random(1, #colors)]
	part.Size = Vector3.new(
		math.random(1, 4),
		math.random(1, 4),
		math.random(1, 4)
	)
	part.Position = Vector3.new(
		math.random(-50, 50),
		math.random(10, 100),
		math.random(-50, 50)
	)
	part.CanCollide = false
	part.Parent = workspace
	
	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1
	
	return part
end

function BenchmarkTests.GeometryTest:cleanup()
	for _, obj in ipairs(self.spawnedObjects) do
		if obj and obj.Parent then
			obj:Destroy()
		end
	end
	self.spawnedObjects = {}
	self.objectCount = 0
end

-- Lighting/Effects Stress Test
BenchmarkTests.LightingTest = {}
BenchmarkTests.LightingTest.__index = BenchmarkTests.LightingTest

function BenchmarkTests.LightingTest.new()
	local self = setmetatable({}, BenchmarkTests.LightingTest)
	self.spawnedObjects = {}
	self.objectCount = 0
	return self
end

function BenchmarkTests.LightingTest:spawnObject()
	local effectTypes = {"PointLight", "SpotLight", "ParticleEmitter"}
	local effectType = effectTypes[math.random(1, #effectTypes)]
	
	-- Create a part to hold the effect
	local part = Instance.new("Part")
	part.Name = "LightingTest_" .. self.objectCount
	part.Size = Vector3.new(1, 1, 1)
	part.Position = Vector3.new(
		math.random(-30, 30),
		math.random(5, 50),
		math.random(-30, 30)
	)
	part.CanCollide = false
	part.Transparency = 0.8
	part.Parent = workspace
	
	local effect
	if effectType == "PointLight" then
		effect = Instance.new("PointLight")
		effect.Color = Color3.fromRGB(math.random(0, 255), math.random(0, 255), math.random(0, 255))
		effect.Brightness = math.random(1, 3)
		effect.Range = math.random(10, 30)
	elseif effectType == "SpotLight" then
		effect = Instance.new("SpotLight")
		effect.Color = Color3.fromRGB(math.random(0, 255), math.random(0, 255), math.random(0, 255))
		effect.Brightness = math.random(1, 3)
		effect.Range = math.random(10, 30)
		effect.Angle = math.random(30, 120)
	else -- ParticleEmitter
		effect = Instance.new("ParticleEmitter")
		effect.Rate = math.random(10, 50)
		effect.Lifetime = NumberRange.new(1, 3)
		effect.Speed = NumberRange.new(5, 15)
	end
	
	effect.Parent = part
	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1
	
	return part
end

function BenchmarkTests.LightingTest:cleanup()
	for _, obj in ipairs(self.spawnedObjects) do
		if obj and obj.Parent then
			obj:Destroy()
		end
	end
	self.spawnedObjects = {}
	self.objectCount = 0
end

-- Physics Stress Test
BenchmarkTests.PhysicsTest = {}
BenchmarkTests.PhysicsTest.__index = BenchmarkTests.PhysicsTest

function BenchmarkTests.PhysicsTest.new()
	local self = setmetatable({}, BenchmarkTests.PhysicsTest)
	self.spawnedObjects = {}
	self.objectCount = 0
	return self
end

function BenchmarkTests.PhysicsTest:spawnObject()
	local part = Instance.new("Part")
	part.Name = "PhysicsTest_" .. self.objectCount
	part.Size = Vector3.new(
		math.random(1, 3),
		math.random(1, 3),
		math.random(1, 3)
	)
	part.Position = Vector3.new(
		math.random(-40, 40),
		math.random(20, 80),
		math.random(-40, 40)
	)
	part.CanCollide = true
	part.Material = Enum.Material.Metal

	-- Add BodyVelocity for random movement
	local bodyVelocity = Instance.new("BodyVelocity")
	bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
	bodyVelocity.Velocity = Vector3.new(
		math.random(-20, 20),
		math.random(-10, 10),
		math.random(-20, 20)
	)
	bodyVelocity.Parent = part

	-- Vary density
	local specialMesh = Instance.new("SpecialMesh")
	specialMesh.MeshType = Enum.MeshType.Brick
	specialMesh.Parent = part

	part.Parent = workspace

	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1

	return part
end

function BenchmarkTests.PhysicsTest:cleanup()
	for _, obj in ipairs(self.spawnedObjects) do
		if obj and obj.Parent then
			obj:Destroy()
		end
	end
	self.spawnedObjects = {}
	self.objectCount = 0
end

-- Rendering Stress Test
BenchmarkTests.RenderingTest = {}
BenchmarkTests.RenderingTest.__index = BenchmarkTests.RenderingTest

function BenchmarkTests.RenderingTest.new()
	local self = setmetatable({}, BenchmarkTests.RenderingTest)
	self.spawnedObjects = {}
	self.objectCount = 0
	return self
end

function BenchmarkTests.RenderingTest:spawnObject()
	local part = Instance.new("Part")
	part.Name = "RenderingTest_" .. self.objectCount
	part.Size = Vector3.new(
		math.random(2, 6),
		math.random(2, 6),
		math.random(2, 6)
	)
	part.Position = Vector3.new(
		math.random(-60, 60),
		math.random(5, 60),
		math.random(-60, 60)
	)
	part.CanCollide = false

	-- Complex materials and effects
	local materials = {
		Enum.Material.ForceField,
		Enum.Material.Glass,
		Enum.Material.Neon,
		Enum.Material.Metal,
		Enum.Material.DiamondPlate
	}
	part.Material = materials[math.random(1, #materials)]
	part.Transparency = math.random(0, 70) / 100 -- 0% to 70% transparency
	part.Reflectance = math.random(0, 50) / 100 -- 0% to 50% reflectance

	-- Add texture/decal
	if math.random(1, 3) == 1 then
		local decal = Instance.new("Decal")
		decal.Face = Enum.NormalId.Front
		decal.Texture = "rbxasset://textures/face.png"
		decal.Parent = part
	end

	-- Add mesh for complexity
	local mesh = Instance.new("SpecialMesh")
	local meshTypes = {Enum.MeshType.Sphere, Enum.MeshType.Cylinder, Enum.MeshType.Wedge}
	mesh.MeshType = meshTypes[math.random(1, #meshTypes)]
	mesh.Scale = Vector3.new(
		math.random(50, 150) / 100,
		math.random(50, 150) / 100,
		math.random(50, 150) / 100
	)
	mesh.Parent = part

	part.Parent = workspace

	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1

	return part
end

function BenchmarkTests.RenderingTest:cleanup()
	for _, obj in ipairs(self.spawnedObjects) do
		if obj and obj.Parent then
			obj:Destroy()
		end
	end
	self.spawnedObjects = {}
	self.objectCount = 0
end

-- Main Benchmark Manager Implementation
function BenchmarkManager.new()
	local self = setmetatable({}, BenchmarkManager)

	-- Initialize components
	self.performanceMonitor = PerformanceMonitor.new()
	self.tests = {
		geometry = BenchmarkTests.GeometryTest.new(),
		lighting = BenchmarkTests.LightingTest.new(),
		physics = BenchmarkTests.PhysicsTest.new(),
		rendering = BenchmarkTests.RenderingTest.new()
	}

	-- Benchmark state
	self.isRunning = false
	self.currentTest = nil
	self.currentTestName = ""
	self.spawnRate = CONFIG.SPAWN_RATES.MEDIUM
	self.maxObjects = CONFIG.MAX_OBJECTS_PER_TEST
	self.lastSpawnTime = 0

	-- Results tracking
	self.results = {
		geometry = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false},
		lighting = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false},
		physics = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false},
		rendering = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false}
	}

	-- Device detection
	self.deviceInfo = self:detectDevice()

	return self
end

function BenchmarkManager:detectDevice()
	local info = {
		touchEnabled = UserInputService.TouchEnabled,
		keyboardEnabled = UserInputService.KeyboardEnabled,
		gamepadEnabled = UserInputService.GamepadEnabled,
		platform = "Unknown"
	}

	if info.touchEnabled and not info.keyboardEnabled then
		info.platform = "Mobile"
	elseif info.keyboardEnabled and not info.touchEnabled then
		info.platform = "Desktop"
	elseif info.keyboardEnabled and info.touchEnabled then
		info.platform = "Tablet/Hybrid"
	end

	return info
end

function BenchmarkManager:startBenchmark(testName)
	if self.isRunning then
		return false, "Benchmark already running"
	end

	if not self.tests[testName] then
		return false, "Invalid test name: " .. tostring(testName)
	end

	self.isRunning = true
	self.currentTest = self.tests[testName]
	self.currentTestName = testName
	self.lastSpawnTime = tick()

	-- Reset test
	self.currentTest:cleanup()

	-- Start spawn loop
	self.spawnConnection = RunService.Heartbeat:Connect(function()
		self:updateBenchmark()
	end)

	return true, "Benchmark started: " .. testName
end

function BenchmarkManager:updateBenchmark()
	if not self.isRunning or not self.currentTest then
		return
	end

	local currentTime = tick()
	local fps = self.performanceMonitor:getCurrentFPS()
	local objectCount = self.currentTest.objectCount

	-- Emergency stop condition
	if fps < CONFIG.EMERGENCY_STOP_FPS then
		self:stopBenchmark()
		return
	end

	-- Record FPS thresholds
	local result = self.results[self.currentTestName]
	if fps >= 60 and result.objectsAt60FPS == 0 then
		result.objectsAt60FPS = objectCount
	end
	if fps >= 30 and result.objectsAt30FPS == 0 then
		result.objectsAt30FPS = objectCount
	end

	-- Check if we should spawn more objects
	if currentTime - self.lastSpawnTime >= (1 / self.spawnRate) then
		if objectCount < self.maxObjects then
			self.currentTest:spawnObject()
			self.lastSpawnTime = currentTime
		else
			-- Test completed
			result.maxObjects = objectCount
			result.completed = true
			self:stopBenchmark()
		end
	end
end

function BenchmarkManager:stopBenchmark()
	if not self.isRunning then
		return false, "No benchmark running"
	end

	self.isRunning = false

	if self.spawnConnection then
		self.spawnConnection:Disconnect()
		self.spawnConnection = nil
	end

	-- Record final results
	if self.currentTest and self.currentTestName then
		local result = self.results[self.currentTestName]
		result.maxObjects = self.currentTest.objectCount
		result.completed = true

		-- If thresholds weren't hit, record max objects
		if result.objectsAt60FPS == 0 then
			result.objectsAt60FPS = result.maxObjects
		end
		if result.objectsAt30FPS == 0 then
			result.objectsAt30FPS = result.maxObjects
		end
	end

	return true, "Benchmark stopped"
end

function BenchmarkManager:resetBenchmark()
	self:stopBenchmark()

	-- Clean up all tests
	for _, test in pairs(self.tests) do
		test:cleanup()
	end

	-- Reset results
	for testName, _ in pairs(self.results) do
		self.results[testName] = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false}
	end

	self.currentTest = nil
	self.currentTestName = ""
end

function BenchmarkManager:getResults()
	return self.results
end

function BenchmarkManager:setSpawnRate(rate)
	if CONFIG.SPAWN_RATES[rate] then
		self.spawnRate = CONFIG.SPAWN_RATES[rate]
		return true
	end
	return false
end

function BenchmarkManager:setMaxObjects(max)
	if max > 0 and max <= 5000 then
		self.maxObjects = max
		return true
	end
	return false
end

function BenchmarkManager:getCurrentTestInfo()
	if not self.currentTest or not self.currentTestName then
		return nil
	end

	return {
		name = self.currentTestName,
		objectCount = self.currentTest.objectCount,
		progress = self.currentTest.objectCount / self.maxObjects
	}
end

function BenchmarkManager:getPerformanceData()
	return {
		fps = self.performanceMonitor:getCurrentFPS(),
		memory = self.performanceMonitor:getMemoryUsage(),
		ping = self.performanceMonitor:getPing(),
		rating = self.performanceMonitor:getPerformanceRating()
	}
end

function BenchmarkManager:destroy()
	self:stopBenchmark()

	-- Clean up all tests
	for _, test in pairs(self.tests) do
		test:cleanup()
	end

	-- Clean up performance monitor
	if self.performanceMonitor then
		self.performanceMonitor:destroy()
	end
end

-- User Interface Implementation
local BenchmarkUI = {}
BenchmarkUI.__index = BenchmarkUI

function BenchmarkUI.new(benchmarkManager)
	local self = setmetatable({}, BenchmarkUI)
	self.benchmarkManager = benchmarkManager

	-- Create UI
	self:createUI()

	-- Connect update loop
	self.updateConnection = RunService.Heartbeat:Connect(function()
		self:updateUI()
	end)

	return self
end

function BenchmarkUI:createUI()
	-- Main ScreenGui
	local screenGui = Instance.new("ScreenGui")
	screenGui.Name = "FPSBenchmarkUI"
	screenGui.ResetOnSpawn = false
	screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	screenGui.Parent = playerGui
	self.screenGui = screenGui

	-- Main Frame
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(0, 400, 0, 500)
	mainFrame.Position = UDim2.new(0.5, -200, 0.5, -250)
	mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	mainFrame.BorderSizePixel = 0
	mainFrame.Parent = screenGui
	self.mainFrame = mainFrame

	-- Add UICorner
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = mainFrame

	-- Add UIStroke
	local stroke = Instance.new("UIStroke")
	stroke.Color = Color3.fromRGB(80, 80, 80)
	stroke.Thickness = 2
	stroke.Parent = mainFrame

	-- Title
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Name = "TitleLabel"
	titleLabel.Size = UDim2.new(1, 0, 0, 50)
	titleLabel.Position = UDim2.new(0, 0, 0, 0)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Font = Enum.Font.GothamBold
	titleLabel.Text = "Roblox FPS Benchmark"
	titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	titleLabel.TextSize = 24
	titleLabel.Parent = mainFrame

	-- Performance Display
	local performanceFrame = Instance.new("Frame")
	performanceFrame.Name = "PerformanceFrame"
	performanceFrame.Size = UDim2.new(1, -40, 0, 120)
	performanceFrame.Position = UDim2.new(0, 20, 0, 60)
	performanceFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	performanceFrame.BorderSizePixel = 0
	performanceFrame.Parent = mainFrame
	self.performanceFrame = performanceFrame

	-- Add UICorner to performance frame
	local perfCorner = Instance.new("UICorner")
	perfCorner.CornerRadius = UDim.new(0, 8)
	perfCorner.Parent = performanceFrame

	-- FPS Display
	local fpsLabel = Instance.new("TextLabel")
	fpsLabel.Name = "FPSLabel"
	fpsLabel.Size = UDim2.new(0.5, -10, 0, 30)
	fpsLabel.Position = UDim2.new(0, 10, 0, 10)
	fpsLabel.BackgroundTransparency = 1
	fpsLabel.Font = Enum.Font.Gotham
	fpsLabel.Text = "FPS: 60.0"
	fpsLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	fpsLabel.TextSize = 18
	fpsLabel.TextXAlignment = Enum.TextXAlignment.Left
	fpsLabel.Parent = performanceFrame
	self.fpsLabel = fpsLabel

	-- Memory Display
	local memoryLabel = Instance.new("TextLabel")
	memoryLabel.Name = "MemoryLabel"
	memoryLabel.Size = UDim2.new(0.5, -10, 0, 30)
	memoryLabel.Position = UDim2.new(0.5, 0, 0, 10)
	memoryLabel.BackgroundTransparency = 1
	memoryLabel.Font = Enum.Font.Gotham
	memoryLabel.Text = "Memory: 0 MB"
	memoryLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	memoryLabel.TextSize = 18
	memoryLabel.TextXAlignment = Enum.TextXAlignment.Left
	memoryLabel.Parent = performanceFrame
	self.memoryLabel = memoryLabel

	-- Performance Rating
	local ratingLabel = Instance.new("TextLabel")
	ratingLabel.Name = "RatingLabel"
	ratingLabel.Size = UDim2.new(1, -20, 0, 30)
	ratingLabel.Position = UDim2.new(0, 10, 0, 45)
	ratingLabel.BackgroundTransparency = 1
	ratingLabel.Font = Enum.Font.GothamBold
	ratingLabel.Text = "Performance: Excellent"
	ratingLabel.TextColor3 = Color3.fromRGB(0, 255, 0)
	ratingLabel.TextSize = 16
	ratingLabel.TextXAlignment = Enum.TextXAlignment.Left
	ratingLabel.Parent = performanceFrame
	self.ratingLabel = ratingLabel

	-- Objects Count
	local objectsLabel = Instance.new("TextLabel")
	objectsLabel.Name = "ObjectsLabel"
	objectsLabel.Size = UDim2.new(1, -20, 0, 30)
	objectsLabel.Position = UDim2.new(0, 10, 0, 75)
	objectsLabel.BackgroundTransparency = 1
	objectsLabel.Font = Enum.Font.Gotham
	objectsLabel.Text = "Objects: 0"
	objectsLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	objectsLabel.TextSize = 16
	objectsLabel.TextXAlignment = Enum.TextXAlignment.Left
	objectsLabel.Parent = performanceFrame
	self.objectsLabel = objectsLabel

	-- Test Controls Frame
	local controlsFrame = Instance.new("Frame")
	controlsFrame.Name = "ControlsFrame"
	controlsFrame.Size = UDim2.new(1, -40, 0, 200)
	controlsFrame.Position = UDim2.new(0, 20, 0, 200)
	controlsFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	controlsFrame.BorderSizePixel = 0
	controlsFrame.Parent = mainFrame
	self.controlsFrame = controlsFrame

	-- Add UICorner to controls frame
	local controlsCorner = Instance.new("UICorner")
	controlsCorner.CornerRadius = UDim.new(0, 8)
	controlsCorner.Parent = controlsFrame

	-- Test Buttons
	local testNames = {"geometry", "lighting", "physics", "rendering"}
	local testDisplayNames = {"Geometry Test", "Lighting Test", "Physics Test", "Rendering Test"}

	self.testButtons = {}
	for i, testName in ipairs(testNames) do
		local button = Instance.new("TextButton")
		button.Name = testName .. "Button"
		button.Size = UDim2.new(0.45, 0, 0, 35)
		button.Position = UDim2.new(
			((i - 1) % 2) * 0.5 + 0.05,
			0,
			math.floor((i - 1) / 2) * 0.25 + 0.1,
			0
		)
		button.BackgroundColor3 = Color3.fromRGB(70, 130, 180)
		button.BorderSizePixel = 0
		button.Font = Enum.Font.Gotham
		button.Text = testDisplayNames[i]
		button.TextColor3 = Color3.fromRGB(255, 255, 255)
		button.TextSize = 14
		button.Parent = controlsFrame

		-- Add UICorner
		local buttonCorner = Instance.new("UICorner")
		buttonCorner.CornerRadius = UDim.new(0, 6)
		buttonCorner.Parent = button

		-- Connect button
		button.MouseButton1Click:Connect(function()
			self:startTest(testName)
		end)

		self.testButtons[testName] = button
	end

	-- Control Buttons Frame
	local buttonsFrame = Instance.new("Frame")
	buttonsFrame.Name = "ButtonsFrame"
	buttonsFrame.Size = UDim2.new(1, -20, 0, 40)
	buttonsFrame.Position = UDim2.new(0, 10, 0, 150)
	buttonsFrame.BackgroundTransparency = 1
	buttonsFrame.Parent = controlsFrame

	-- Stop Button
	local stopButton = Instance.new("TextButton")
	stopButton.Name = "StopButton"
	stopButton.Size = UDim2.new(0.3, -5, 1, 0)
	stopButton.Position = UDim2.new(0, 0, 0, 0)
	stopButton.BackgroundColor3 = Color3.fromRGB(220, 20, 60)
	stopButton.BorderSizePixel = 0
	stopButton.Font = Enum.Font.Gotham
	stopButton.Text = "Stop"
	stopButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	stopButton.TextSize = 14
	stopButton.Parent = buttonsFrame
	self.stopButton = stopButton

	-- Add UICorner
	local stopCorner = Instance.new("UICorner")
	stopCorner.CornerRadius = UDim.new(0, 6)
	stopCorner.Parent = stopButton

	-- Reset Button
	local resetButton = Instance.new("TextButton")
	resetButton.Name = "ResetButton"
	resetButton.Size = UDim2.new(0.3, -5, 1, 0)
	resetButton.Position = UDim2.new(0.35, 0, 0, 0)
	resetButton.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
	resetButton.BorderSizePixel = 0
	resetButton.Font = Enum.Font.Gotham
	resetButton.Text = "Reset"
	resetButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	resetButton.TextSize = 14
	resetButton.Parent = buttonsFrame
	self.resetButton = resetButton

	-- Add UICorner
	local resetCorner = Instance.new("UICorner")
	resetCorner.CornerRadius = UDim.new(0, 6)
	resetCorner.Parent = resetButton

	-- Settings Button
	local settingsButton = Instance.new("TextButton")
	settingsButton.Name = "SettingsButton"
	settingsButton.Size = UDim2.new(0.3, -5, 1, 0)
	settingsButton.Position = UDim2.new(0.7, 0, 0, 0)
	settingsButton.BackgroundColor3 = Color3.fromRGB(128, 128, 128)
	settingsButton.BorderSizePixel = 0
	settingsButton.Font = Enum.Font.Gotham
	settingsButton.Text = "Settings"
	settingsButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	settingsButton.TextSize = 14
	settingsButton.Parent = buttonsFrame
	self.settingsButton = settingsButton

	-- Add UICorner
	local settingsCorner = Instance.new("UICorner")
	settingsCorner.CornerRadius = UDim.new(0, 6)
	settingsCorner.Parent = settingsButton

	-- Connect control buttons
	stopButton.MouseButton1Click:Connect(function()
		self.benchmarkManager:stopBenchmark()
	end)

	resetButton.MouseButton1Click:Connect(function()
		self.benchmarkManager:resetBenchmark()
	end)

	settingsButton.MouseButton1Click:Connect(function()
		self:toggleSettings()
	end)
end

function BenchmarkUI:startTest(testName)
	local success, message = self.benchmarkManager:startBenchmark(testName)
	if success then
		-- Update button states
		for name, button in pairs(self.testButtons) do
			if name == testName then
				button.BackgroundColor3 = Color3.fromRGB(34, 139, 34) -- Green for active
				button.Text = button.Text .. " (Running)"
			else
				button.BackgroundColor3 = Color3.fromRGB(70, 70, 70) -- Gray for disabled
			end
		end
	end
end

function BenchmarkUI:updateUI()
	-- Update performance data
	local perfData = self.benchmarkManager:getPerformanceData()

	self.fpsLabel.Text = string.format("FPS: %.1f", perfData.fps)
	self.memoryLabel.Text = string.format("Memory: %.1f MB", perfData.memory / 1024 / 1024)

	local rating, color = perfData.rating()
	self.ratingLabel.Text = "Performance: " .. rating
	self.ratingLabel.TextColor3 = color

	-- Update current test info
	local testInfo = self.benchmarkManager:getCurrentTestInfo()
	if testInfo then
		self.objectsLabel.Text = string.format("Objects: %d (%.1f%%)",
			testInfo.objectCount, testInfo.progress * 100)
	else
		self.objectsLabel.Text = "Objects: 0"
	end

	-- Update button states if not running
	if not self.benchmarkManager.isRunning then
		for name, button in pairs(self.testButtons) do
			button.BackgroundColor3 = Color3.fromRGB(70, 130, 180)
			local displayNames = {
				geometry = "Geometry Test",
				lighting = "Lighting Test",
				physics = "Physics Test",
				rendering = "Rendering Test"
			}
			button.Text = displayNames[name]
		end
	end
end

function BenchmarkUI:toggleSettings()
	-- Settings panel implementation would go here
	-- For now, just show a simple message
	print("Settings panel - Feature coming soon!")
end

function BenchmarkUI:destroy()
	if self.updateConnection then
		self.updateConnection:Disconnect()
	end

	if self.screenGui then
		self.screenGui:Destroy()
	end
end

-- Results Display UI
function BenchmarkUI:showResults()
	local results = self.benchmarkManager:getResults()

	-- Create results window
	local resultsGui = Instance.new("ScreenGui")
	resultsGui.Name = "BenchmarkResults"
	resultsGui.Parent = playerGui

	local resultsFrame = Instance.new("Frame")
	resultsFrame.Size = UDim2.new(0, 500, 0, 400)
	resultsFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
	resultsFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
	resultsFrame.BorderSizePixel = 0
	resultsFrame.Parent = resultsGui

	-- Add UICorner
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = resultsFrame

	-- Title
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0, 50)
	title.BackgroundTransparency = 1
	title.Font = Enum.Font.GothamBold
	title.Text = "Benchmark Results"
	title.TextColor3 = Color3.fromRGB(255, 255, 255)
	title.TextSize = 24
	title.Parent = resultsFrame

	-- Results content
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Size = UDim2.new(1, -20, 1, -100)
	scrollFrame.Position = UDim2.new(0, 10, 0, 60)
	scrollFrame.BackgroundTransparency = 1
	scrollFrame.BorderSizePixel = 0
	scrollFrame.ScrollBarThickness = 8
	scrollFrame.Parent = resultsFrame

	-- Add results text
	local yPos = 0
	for testName, result in pairs(results) do
		if result.completed then
			local testLabel = Instance.new("TextLabel")
			testLabel.Size = UDim2.new(1, 0, 0, 80)
			testLabel.Position = UDim2.new(0, 0, 0, yPos)
			testLabel.BackgroundTransparency = 1
			testLabel.Font = Enum.Font.Gotham
			testLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
			testLabel.TextSize = 14
			testLabel.TextXAlignment = Enum.TextXAlignment.Left
			testLabel.TextYAlignment = Enum.TextYAlignment.Top
			testLabel.Text = string.format(
				"%s Test:\n  • Objects at 60 FPS: %d\n  • Objects at 30 FPS: %d\n  • Max Objects: %d",
				testName:gsub("^%l", string.upper),
				result.objectsAt60FPS,
				result.objectsAt30FPS,
				result.maxObjects
			)
			testLabel.Parent = scrollFrame
			yPos = yPos + 90
		end
	end

	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 100, 0, 30)
	closeButton.Position = UDim2.new(0.5, -50, 1, -40)
	closeButton.BackgroundColor3 = Color3.fromRGB(220, 20, 60)
	closeButton.BorderSizePixel = 0
	closeButton.Font = Enum.Font.Gotham
	closeButton.Text = "Close"
	closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	closeButton.TextSize = 14
	closeButton.Parent = resultsFrame

	-- Add UICorner
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton

	closeButton.MouseButton1Click:Connect(function()
		resultsGui:Destroy()
	end)
end

-- Initialize the benchmark system
local benchmarkManager = BenchmarkManager.new()
local benchmarkUI = BenchmarkUI.new(benchmarkManager)

-- Cleanup on player leaving
Players.PlayerRemoving:Connect(function(leavingPlayer)
	if leavingPlayer == player then
		benchmarkManager:destroy()
		benchmarkUI:destroy()
	end
end)

print("FPS Benchmark System Loaded Successfully!")
print("Device Info:", HttpService:JSONEncode(benchmarkManager.deviceInfo))
