--[[
	Roblox FPS Benchmarking System - Main LocalScript
	
	This script provides comprehensive client-side performance testing
	by progressively spawning objects and monitoring frame rate impact.
	
	Place this in StarterPlayer > StarterPlayerScripts
]]

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local Stats = game:GetService("Stats")
local TweenService = game:GetService("TweenService")
local HttpService = game:GetService("HttpService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Player setup
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for modules to load (they'll be in ReplicatedStorage)
local benchmarkModules = ReplicatedStorage:WaitForChild("BenchmarkModules")
local PerformanceMonitor = require(benchmarkModules:WaitFor<PERSON>hild("PerformanceMonitor"))
local BenchmarkTests = require(benchmarkModules:WaitForChild("BenchmarkTests"))
local BenchmarkUI = require(benchmarkModules:WaitForChild("BenchmarkUI"))
local BenchmarkConfig = require(benchmarkModules:WaitForChild("BenchmarkConfig"))

-- Core Benchmark Manager
local BenchmarkManager = {}
BenchmarkManager.__index = BenchmarkManager

function BenchmarkManager.new()
	local self = setmetatable({}, BenchmarkManager)
	
	-- Initialize components
	self.performanceMonitor = PerformanceMonitor.new()
	self.tests = {
		geometry = BenchmarkTests.GeometryTest.new(),
		lighting = BenchmarkTests.LightingTest.new(),
		physics = BenchmarkTests.PhysicsTest.new(),
		rendering = BenchmarkTests.RenderingTest.new()
	}
	
	-- Benchmark state
	self.isRunning = false
	self.currentTest = nil
	self.currentTestName = ""
	self.spawnRate = BenchmarkConfig.SPAWN_RATES.MEDIUM
	self.maxObjects = BenchmarkConfig.MAX_OBJECTS_PER_TEST
	self.lastSpawnTime = 0
	
	-- Results tracking
	self.results = {
		geometry = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false},
		lighting = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false},
		physics = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false},
		rendering = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false}
	}
	
	-- Device detection
	self.deviceInfo = self:detectDevice()
	
	return self
end

function BenchmarkManager:detectDevice()
	local info = {
		touchEnabled = UserInputService.TouchEnabled,
		keyboardEnabled = UserInputService.KeyboardEnabled,
		gamepadEnabled = UserInputService.GamepadEnabled,
		platform = "Unknown"
	}
	
	if info.touchEnabled and not info.keyboardEnabled then
		info.platform = "Mobile"
	elseif info.keyboardEnabled and not info.touchEnabled then
		info.platform = "Desktop"
	elseif info.keyboardEnabled and info.touchEnabled then
		info.platform = "Tablet/Hybrid"
	end
	
	return info
end

function BenchmarkManager:startBenchmark(testName)
	if self.isRunning then
		return false, "Benchmark already running"
	end
	
	if not self.tests[testName] then
		return false, "Invalid test name: " .. tostring(testName)
	end
	
	self.isRunning = true
	self.currentTest = self.tests[testName]
	self.currentTestName = testName
	self.lastSpawnTime = tick()
	
	-- Reset test
	self.currentTest:cleanup()
	
	-- Start spawn loop
	self.spawnConnection = RunService.Heartbeat:Connect(function()
		self:updateBenchmark()
	end)
	
	return true, "Benchmark started: " .. testName
end

function BenchmarkManager:updateBenchmark()
	if not self.isRunning or not self.currentTest then
		return
	end
	
	local currentTime = tick()
	local fps = self.performanceMonitor:getCurrentFPS()
	local objectCount = self.currentTest.objectCount
	
	-- Emergency stop condition
	if fps < BenchmarkConfig.EMERGENCY_STOP_FPS then
		self:stopBenchmark()
		return
	end
	
	-- Record FPS thresholds
	local result = self.results[self.currentTestName]
	if fps >= 60 and result.objectsAt60FPS == 0 then
		result.objectsAt60FPS = objectCount
	end
	if fps >= 30 and result.objectsAt30FPS == 0 then
		result.objectsAt30FPS = objectCount
	end
	
	-- Check if we should spawn more objects
	if currentTime - self.lastSpawnTime >= (1 / self.spawnRate) then
		if objectCount < self.maxObjects then
			self.currentTest:spawnObject()
			self.lastSpawnTime = currentTime
		else
			-- Test completed
			result.maxObjects = objectCount
			result.completed = true
			self:stopBenchmark()
		end
	end
end

function BenchmarkManager:stopBenchmark()
	if not self.isRunning then
		return false, "No benchmark running"
	end
	
	self.isRunning = false
	
	if self.spawnConnection then
		self.spawnConnection:Disconnect()
		self.spawnConnection = nil
	end
	
	-- Record final results
	if self.currentTest and self.currentTestName then
		local result = self.results[self.currentTestName]
		result.maxObjects = self.currentTest.objectCount
		result.completed = true
		
		-- If thresholds weren't hit, record max objects
		if result.objectsAt60FPS == 0 then
			result.objectsAt60FPS = result.maxObjects
		end
		if result.objectsAt30FPS == 0 then
			result.objectsAt30FPS = result.maxObjects
		end
	end
	
	return true, "Benchmark stopped"
end

function BenchmarkManager:resetBenchmark()
	self:stopBenchmark()
	
	-- Clean up all tests
	for _, test in pairs(self.tests) do
		test:cleanup()
	end
	
	-- Reset results
	for testName, _ in pairs(self.results) do
		self.results[testName] = {objectsAt30FPS = 0, objectsAt60FPS = 0, maxObjects = 0, completed = false}
	end
	
	self.currentTest = nil
	self.currentTestName = ""
end

function BenchmarkManager:getResults()
	return self.results
end

function BenchmarkManager:setSpawnRate(rate)
	if BenchmarkConfig.SPAWN_RATES[rate] then
		self.spawnRate = BenchmarkConfig.SPAWN_RATES[rate]
		return true
	end
	return false
end

function BenchmarkManager:setMaxObjects(max)
	if max > 0 and max <= 5000 then
		self.maxObjects = max
		return true
	end
	return false
end

function BenchmarkManager:getCurrentTestInfo()
	if not self.currentTest or not self.currentTestName then
		return nil
	end
	
	return {
		name = self.currentTestName,
		objectCount = self.currentTest.objectCount,
		progress = self.currentTest.objectCount / self.maxObjects
	}
end

function BenchmarkManager:getPerformanceData()
	return {
		fps = self.performanceMonitor:getCurrentFPS(),
		memory = self.performanceMonitor:getMemoryUsage(),
		ping = self.performanceMonitor:getPing(),
		rating = self.performanceMonitor:getPerformanceRating()
	}
end

function BenchmarkManager:destroy()
	self:stopBenchmark()
	
	-- Clean up all tests
	for _, test in pairs(self.tests) do
		test:cleanup()
	end
	
	-- Clean up performance monitor
	if self.performanceMonitor then
		self.performanceMonitor:destroy()
	end
end

-- Initialize the benchmark system
local benchmarkManager = BenchmarkManager.new()
local benchmarkUI = BenchmarkUI.new(benchmarkManager)

-- Cleanup on player leaving
Players.PlayerRemoving:Connect(function(leavingPlayer)
	if leavingPlayer == player then
		benchmarkManager:destroy()
		benchmarkUI:destroy()
	end
end)

print("FPS Benchmark System Loaded Successfully!")
print("Device Info:", HttpService:JSONEncode(benchmarkManager.deviceInfo))

-- Export for debugging
_G.BenchmarkManager = benchmarkManager
_G.BenchmarkUI = benchmarkUI
