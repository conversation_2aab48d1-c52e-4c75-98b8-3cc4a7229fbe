# FPS Benchmark Installation Guide

## Quick Start with Argon

If you're using Argon (recommended):

1. **Ensure Argon is running** and connected to Roblox Studio
2. **Open your place** in Roblox Studio
3. **Files will automatically sync** from this project structure:
   ```
   src/
   ├── ReplicatedStorage/
   │   └── BenchmarkModules/
   │       ├── BenchmarkConfig.lua (ModuleScript)
   │       ├── PerformanceMonitor.lua (ModuleScript)
   │       ├── BenchmarkTests.lua (ModuleScript)
   │       └── BenchmarkUI.lua (ModuleScript)
   └── StarterPlayer/
       └── StarterPlayerScripts/
           └── FPSBenchmarkMain.lua (LocalScript)
   ```

4. **Test the game** - Join and the benchmark UI should appear automatically

## Manual Installation Steps

### Step 1: Create Folder Structure

In Roblox Studio, create this exact folder structure:

```
ReplicatedStorage
└── BenchmarkModules (Folder)

StarterPlayer
└── StarterPlayerScripts (should already exist)
```

### Step 2: Create ModuleScripts

In the `BenchmarkModules` folder, create these **ModuleScripts**:

1. **BenchmarkConfig** (ModuleScript)
2. **PerformanceMonitor** (ModuleScript)  
3. **BenchmarkTests** (ModuleScript)
4. **BenchmarkUI** (ModuleScript)

### Step 3: Create LocalScript

In `StarterPlayerScripts`, create:

1. **FPSBenchmarkMain** (LocalScript)

### Step 4: Copy Code

Copy the code from each corresponding `.lua` file in this repository to the scripts you created:

- `src/ReplicatedStorage/BenchmarkModules/BenchmarkConfig.lua` → BenchmarkConfig ModuleScript
- `src/ReplicatedStorage/BenchmarkModules/PerformanceMonitor.lua` → PerformanceMonitor ModuleScript
- `src/ReplicatedStorage/BenchmarkModules/BenchmarkTests.lua` → BenchmarkTests ModuleScript
- `src/ReplicatedStorage/BenchmarkModules/BenchmarkUI.lua` → BenchmarkUI ModuleScript
- `src/StarterPlayer/StarterPlayerScripts/FPSBenchmarkMain.lua` → FPSBenchmarkMain LocalScript

### Step 5: Verify Installation

1. **Play test your game**
2. **Look for the benchmark UI** in the top-center of your screen
3. **Check the Developer Console (F9)** for any error messages
4. **You should see**: "FPS Benchmark System Loaded Successfully!" in the output

## Troubleshooting

### UI Doesn't Appear

**Check these common issues:**

1. **Script Type**: Ensure `FPSBenchmarkMain` is a **LocalScript**, not a regular Script
2. **Location**: LocalScript must be in `StarterPlayer > StarterPlayerScripts`
3. **ModuleScript Types**: All files in BenchmarkModules must be **ModuleScripts**
4. **Folder Name**: Ensure the folder is named exactly `BenchmarkModules` (case-sensitive)

### Error Messages

**"Module not found" errors:**
- Check that all ModuleScripts are in the correct `ReplicatedStorage/BenchmarkModules/` location
- Verify folder and script names match exactly (case-sensitive)

**"Attempt to call a nil value" errors:**
- Ensure all ModuleScripts have the correct code copied
- Check that each ModuleScript ends with `return ModuleName`

### Performance Issues

**If the benchmark seems to lag or crash:**
- The system has built-in safety limits
- Emergency stop triggers at 5 FPS
- Maximum 1000 objects per test by default
- Adjust limits in `BenchmarkConfig.lua` if needed

## Customization

### Adjusting Test Parameters

Edit the `BenchmarkConfig` ModuleScript to customize:

```lua
-- Change spawn rates
SPAWN_RATES = {
    SLOW = 1,      -- 1 object per second
    MEDIUM = 3,    -- 3 objects per second (default)
    FAST = 5,      -- 5 objects per second
    EXTREME = 10   -- 10 objects per second
}

-- Adjust object limits
MAX_OBJECTS_PER_TEST = 1000  -- Default limit
ABSOLUTE_MAX_OBJECTS = 5000  -- Safety limit

-- Modify FPS thresholds
FPS_THRESHOLDS = {
    EXCELLENT = 60,
    GOOD = 45,
    FAIR = 30,
    POOR = 15
}
```

### UI Customization

Modify colors and sizes in `BenchmarkConfig.lua`:

```lua
UI = {
    MAIN_FRAME_SIZE = {400, 500},  -- Width, Height
    CORNER_RADIUS = 10,
    
    COLORS = {
        BACKGROUND = Color3.fromRGB(40, 40, 40),
        BUTTON_PRIMARY = Color3.fromRGB(70, 130, 180),
        -- ... more color options
    }
}
```

## Testing Your Installation

### Basic Test Sequence

1. **Join your game**
2. **Verify UI appears** with "Roblox FPS Benchmark" title
3. **Check performance display** shows current FPS and memory
4. **Click "Geometry Test"** button
5. **Watch objects spawn** and FPS counter update
6. **Click "Stop"** to halt the test
7. **Click "Results"** to view test data
8. **Click "Reset"** to clear everything

### Expected Behavior

- **Objects should spawn** progressively in the workspace
- **FPS should decrease** as more objects are added
- **Performance rating** should change color based on FPS
- **Emergency stop** should trigger if FPS drops too low
- **Memory usage** should increase during tests

## Support

If you encounter issues:

1. **Check the Developer Console (F9)** for error messages
2. **Verify the installation steps** were followed exactly
3. **Test in a new, empty place** to rule out conflicts
4. **Check that Roblox Studio is up to date**

The benchmark system is designed to be robust and should work in most Roblox environments. If problems persist, the issue is likely with the installation setup rather than the code itself.
