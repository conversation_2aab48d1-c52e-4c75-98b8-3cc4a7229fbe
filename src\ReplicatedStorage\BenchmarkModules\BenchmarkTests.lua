--[[
	Benchmark Tests Module
	
	Contains all four benchmark test categories:
	1. Geometry Stress Test - Basic part rendering
	2. Lighting/Effects Stress Test - Lights and particles
	3. Physics Stress Test - Physics simulation
	4. Rendering Stress Test - Complex materials and textures
]]

local BenchmarkConfig = require(script.Parent.BenchmarkConfig)

local BenchmarkTests = {}

-- Utility function for random values in range
local function randomInRange(min, max)
	return math.random() * (max - min) + min
end

local function randomFromArray(array)
	return array[math.random(1, #array)]
end

-- Base Test Class
local BaseTest = {}
BaseTest.__index = BaseTest

function BaseTest.new()
	local self = setmetatable({}, BaseTest)
	self.spawnedObjects = {}
	self.objectCount = 0
	return self
end

function BaseTest:cleanup()
	-- Clean up in batches to prevent lag
	local batchSize = BenchmarkConfig.SAFETY.CLEANUP_BATCH_SIZE
	local delay = BenchmarkConfig.SAFETY.CLEANUP_DELAY
	
	for i = 1, #self.spawnedObjects, batchSize do
		local batch = {}
		for j = i, math.min(i + batchSize - 1, #self.spawnedObjects) do
			table.insert(batch, self.spawnedObjects[j])
		end
		
		-- Clean up this batch
		for _, obj in ipairs(batch) do
			if obj and obj.Parent then
				obj:Destroy()
			end
		end
		
		-- Small delay between batches
		if i + batchSize <= #self.spawnedObjects then
			wait(delay)
		end
	end
	
	self.spawnedObjects = {}
	self.objectCount = 0
end

-- Geometry Stress Test
BenchmarkTests.GeometryTest = setmetatable({}, {__index = BaseTest})
BenchmarkTests.GeometryTest.__index = BenchmarkTests.GeometryTest

function BenchmarkTests.GeometryTest.new()
	local self = setmetatable(BaseTest.new(), BenchmarkTests.GeometryTest)
	return self
end

function BenchmarkTests.GeometryTest:spawnObject()
	local config = BenchmarkConfig.TESTS.GEOMETRY
	
	local part = Instance.new("Part")
	part.Name = "GeometryTest_" .. self.objectCount
	
	-- Random shape
	part.Shape = Enum.PartType[randomFromArray(config.shapes)]
	
	-- Random material and color
	part.Material = randomFromArray(config.materials)
	part.Color = randomFromArray(config.colors)
	
	-- Random size
	part.Size = Vector3.new(
		randomInRange(config.sizeRange.min, config.sizeRange.max),
		randomInRange(config.sizeRange.min, config.sizeRange.max),
		randomInRange(config.sizeRange.min, config.sizeRange.max)
	)
	
	-- Random position
	part.Position = Vector3.new(
		randomInRange(config.spawnArea.x[1], config.spawnArea.x[2]),
		randomInRange(config.spawnArea.y[1], config.spawnArea.y[2]),
		randomInRange(config.spawnArea.z[1], config.spawnArea.z[2])
	)
	
	part.CanCollide = false
	part.Parent = workspace
	
	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1
	
	return part
end

-- Lighting/Effects Stress Test
BenchmarkTests.LightingTest = setmetatable({}, {__index = BaseTest})
BenchmarkTests.LightingTest.__index = BenchmarkTests.LightingTest

function BenchmarkTests.LightingTest.new()
	local self = setmetatable(BaseTest.new(), BenchmarkTests.LightingTest)
	return self
end

function BenchmarkTests.LightingTest:spawnObject()
	local config = BenchmarkConfig.TESTS.LIGHTING
	local effectType = randomFromArray(config.effectTypes)
	
	-- Create a part to hold the effect
	local part = Instance.new("Part")
	part.Name = "LightingTest_" .. self.objectCount
	part.Size = Vector3.new(1, 1, 1)
	part.Position = Vector3.new(
		randomInRange(config.spawnArea.x[1], config.spawnArea.x[2]),
		randomInRange(config.spawnArea.y[1], config.spawnArea.y[2]),
		randomInRange(config.spawnArea.z[1], config.spawnArea.z[2])
	)
	part.CanCollide = false
	part.Transparency = 0.8
	part.Parent = workspace
	
	local effect
	if effectType == "PointLight" then
		effect = Instance.new("PointLight")
		effect.Color = Color3.fromRGB(math.random(0, 255), math.random(0, 255), math.random(0, 255))
		effect.Brightness = randomInRange(config.lightSettings.brightnessRange[1], config.lightSettings.brightnessRange[2])
		effect.Range = randomInRange(config.lightSettings.rangeMin, config.lightSettings.rangeMax)
		
	elseif effectType == "SpotLight" then
		effect = Instance.new("SpotLight")
		effect.Color = Color3.fromRGB(math.random(0, 255), math.random(0, 255), math.random(0, 255))
		effect.Brightness = randomInRange(config.lightSettings.brightnessRange[1], config.lightSettings.brightnessRange[2])
		effect.Range = randomInRange(config.lightSettings.rangeMin, config.lightSettings.rangeMax)
		effect.Angle = randomInRange(config.lightSettings.spotAngleRange[1], config.lightSettings.spotAngleRange[2])
		
	else -- ParticleEmitter
		effect = Instance.new("ParticleEmitter")
		effect.Rate = randomInRange(config.particleSettings.rateRange[1], config.particleSettings.rateRange[2])
		effect.Lifetime = NumberRange.new(
			config.particleSettings.lifetimeRange[1],
			config.particleSettings.lifetimeRange[2]
		)
		effect.Speed = NumberRange.new(
			config.particleSettings.speedRange[1],
			config.particleSettings.speedRange[2]
		)
	end
	
	effect.Parent = part
	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1
	
	return part
end

-- Physics Stress Test
BenchmarkTests.PhysicsTest = setmetatable({}, {__index = BaseTest})
BenchmarkTests.PhysicsTest.__index = BenchmarkTests.PhysicsTest

function BenchmarkTests.PhysicsTest.new()
	local self = setmetatable(BaseTest.new(), BenchmarkTests.PhysicsTest)
	return self
end

function BenchmarkTests.PhysicsTest:spawnObject()
	local config = BenchmarkConfig.TESTS.PHYSICS
	
	local part = Instance.new("Part")
	part.Name = "PhysicsTest_" .. self.objectCount
	part.Size = Vector3.new(
		randomInRange(config.sizeRange.min, config.sizeRange.max),
		randomInRange(config.sizeRange.min, config.sizeRange.max),
		randomInRange(config.sizeRange.min, config.sizeRange.max)
	)
	part.Position = Vector3.new(
		randomInRange(config.spawnArea.x[1], config.spawnArea.x[2]),
		randomInRange(config.spawnArea.y[1], config.spawnArea.y[2]),
		randomInRange(config.spawnArea.z[1], config.spawnArea.z[2])
	)
	part.CanCollide = true
	part.Material = Enum.Material.Metal
	
	-- Add BodyVelocity for random movement
	local bodyVelocity = Instance.new("BodyVelocity")
	bodyVelocity.MaxForce = Vector3.new(config.forceStrength, config.forceStrength, config.forceStrength)
	bodyVelocity.Velocity = Vector3.new(
		randomInRange(config.velocityRange.x[1], config.velocityRange.x[2]),
		randomInRange(config.velocityRange.y[1], config.velocityRange.y[2]),
		randomInRange(config.velocityRange.z[1], config.velocityRange.z[2])
	)
	bodyVelocity.Parent = part
	
	-- Add mesh for visual variety
	local specialMesh = Instance.new("SpecialMesh")
	specialMesh.MeshType = Enum.MeshType.Brick
	specialMesh.Parent = part
	
	part.Parent = workspace
	
	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1

	return part
end

-- Rendering Stress Test
BenchmarkTests.RenderingTest = setmetatable({}, {__index = BaseTest})
BenchmarkTests.RenderingTest.__index = BenchmarkTests.RenderingTest

function BenchmarkTests.RenderingTest.new()
	local self = setmetatable(BaseTest.new(), BenchmarkTests.RenderingTest)
	return self
end

function BenchmarkTests.RenderingTest:spawnObject()
	local config = BenchmarkConfig.TESTS.RENDERING

	local part = Instance.new("Part")
	part.Name = "RenderingTest_" .. self.objectCount
	part.Size = Vector3.new(
		randomInRange(config.sizeRange.min, config.sizeRange.max),
		randomInRange(config.sizeRange.min, config.sizeRange.max),
		randomInRange(config.sizeRange.min, config.sizeRange.max)
	)
	part.Position = Vector3.new(
		randomInRange(config.spawnArea.x[1], config.spawnArea.x[2]),
		randomInRange(config.spawnArea.y[1], config.spawnArea.y[2]),
		randomInRange(config.spawnArea.z[1], config.spawnArea.z[2])
	)
	part.CanCollide = false

	-- Complex materials and effects
	part.Material = randomFromArray(config.materials)
	part.Transparency = randomInRange(config.transparencyRange[1], config.transparencyRange[2])
	part.Reflectance = randomInRange(config.reflectanceRange[1], config.reflectanceRange[2])

	-- Random color
	part.Color = Color3.fromRGB(math.random(0, 255), math.random(0, 255), math.random(0, 255))

	-- Add texture/decal occasionally
	if math.random() < config.decalChance then
		local decal = Instance.new("Decal")
		decal.Face = Enum.NormalId.Front
		decal.Texture = "rbxasset://textures/face.png"
		decal.Parent = part
	end

	-- Add mesh for complexity
	local mesh = Instance.new("SpecialMesh")
	mesh.MeshType = randomFromArray(config.meshTypes)
	mesh.Scale = Vector3.new(
		randomInRange(config.scaleRange[1], config.scaleRange[2]),
		randomInRange(config.scaleRange[1], config.scaleRange[2]),
		randomInRange(config.scaleRange[1], config.scaleRange[2])
	)
	mesh.Parent = part

	part.Parent = workspace

	table.insert(self.spawnedObjects, part)
	self.objectCount = self.objectCount + 1

	return part
end

return BenchmarkTests
