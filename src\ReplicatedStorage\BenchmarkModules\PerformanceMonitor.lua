--[[
	Performance Monitor Module
	
	Handles real-time monitoring of FPS, memory usage, and network statistics.
	Provides smooth FPS calculations and performance rating system.
]]

local RunService = game:GetService("RunService")
local Stats = game:GetService("Stats")

local BenchmarkConfig = require(script.Parent.BenchmarkConfig)

local PerformanceMonitor = {}
PerformanceMonitor.__index = PerformanceMonitor

function PerformanceMonitor.new()
	local self = setmetatable({}, PerformanceMonitor)
	
	-- FPS tracking
	self.frameCount = 0
	self.lastTime = tick()
	self.currentFPS = 60
	self.fpsHistory = {}
	
	-- Performance metrics
	self.memoryUsage = 0
	self.ping = 0
	self.lastSampleTime = tick()
	
	-- Connect to heartbeat for FPS monitoring
	self.heartbeatConnection = RunService.Heartbeat:Connect(function(deltaTime)
		self:updateFPS(deltaTime)
	end)
	
	-- Performance sampling timer
	self.sampleConnection = RunService.Heartbeat:Connect(function()
		if tick() - self.lastSampleTime >= BenchmarkConfig.PERFORMANCE_SAMPLE_RATE then
			self:samplePerformance()
			self.lastSampleTime = tick()
		end
	end)
	
	return self
end

function PerformanceMonitor:updateFPS(deltaTime)
	-- Calculate FPS using delta time
	if deltaTime > 0 then
		local instantFPS = 1 / deltaTime
		
		-- Add to history for smoothing
		table.insert(self.fpsHistory, instantFPS)
		if #self.fpsHistory > BenchmarkConfig.FPS_HISTORY_SIZE then
			table.remove(self.fpsHistory, 1)
		end
		
		-- Calculate smoothed FPS (average of recent samples)
		local sum = 0
		for _, fps in ipairs(self.fpsHistory) do
			sum = sum + fps
		end
		self.currentFPS = sum / #self.fpsHistory
	end
end

function PerformanceMonitor:samplePerformance()
	-- Memory usage sampling
	self:sampleMemoryUsage()
	
	-- Network stats sampling
	self:sampleNetworkStats()
end

function PerformanceMonitor:sampleMemoryUsage()
	-- Try multiple methods to get memory usage
	local memStats = Stats:FindFirstChild("MemoryUsage")
	if memStats then
		self.memoryUsage = memStats.Value
		return
	end
	
	-- Alternative method using PerformanceStats
	local perfStats = Stats:FindFirstChild("PerformanceStats")
	if perfStats then
		local memoryStats = perfStats:FindFirstChild("Memory")
		if memoryStats then
			self.memoryUsage = memoryStats.Value
			return
		end
	end
	
	-- Fallback: estimate based on object count in workspace
	local objectCount = 0
	local function countObjects(parent)
		for _, child in ipairs(parent:GetChildren()) do
			objectCount = objectCount + 1
			if #child:GetChildren() > 0 then
				countObjects(child)
			end
		end
	end
	countObjects(workspace)
	
	-- Rough estimate: ~1KB per object
	self.memoryUsage = objectCount * 1024
end

function PerformanceMonitor:sampleNetworkStats()
	-- Network statistics
	local networkStats = Stats:FindFirstChild("Network")
	if networkStats then
		-- Try to get server stats
		local serverStats = networkStats:FindFirstChild("ServerStatsItem")
		if serverStats then
			local pingStats = serverStats:FindFirstChild("Data Ping")
			if pingStats then
				self.ping = pingStats.Value
				return
			end
		end
		
		-- Alternative ping measurement
		local replicationLag = networkStats:FindFirstChild("ReplicationLag")
		if replicationLag then
			self.ping = replicationLag.Value * 1000 -- Convert to ms
			return
		end
	end
	
	-- Fallback: use a default ping value
	self.ping = 50
end

function PerformanceMonitor:getCurrentFPS()
	return math.floor(self.currentFPS * 10) / 10 -- Round to 1 decimal place
end

function PerformanceMonitor:getMemoryUsage()
	return self.memoryUsage
end

function PerformanceMonitor:getMemoryUsageMB()
	return self.memoryUsage / (1024 * 1024)
end

function PerformanceMonitor:getPing()
	return math.floor(self.ping)
end

function PerformanceMonitor:getPerformanceRating()
	local fps = self:getCurrentFPS()
	
	if fps >= BenchmarkConfig.FPS_THRESHOLDS.EXCELLENT then
		return "Excellent", BenchmarkConfig.UI.COLORS.EXCELLENT
	elseif fps >= BenchmarkConfig.FPS_THRESHOLDS.GOOD then
		return "Good", BenchmarkConfig.UI.COLORS.GOOD
	elseif fps >= BenchmarkConfig.FPS_THRESHOLDS.FAIR then
		return "Fair", BenchmarkConfig.UI.COLORS.FAIR
	else
		return "Poor", BenchmarkConfig.UI.COLORS.POOR
	end
end

function PerformanceMonitor:getDetailedStats()
	return {
		fps = {
			current = self:getCurrentFPS(),
			average = self.currentFPS,
			history = self.fpsHistory
		},
		memory = {
			bytes = self.memoryUsage,
			megabytes = self:getMemoryUsageMB()
		},
		network = {
			ping = self:getPing()
		},
		performance = {
			rating = self:getPerformanceRating()
		}
	}
end

function PerformanceMonitor:isPerformanceAcceptable()
	local fps = self:getCurrentFPS()
	local memoryMB = self:getMemoryUsageMB()
	
	return fps >= BenchmarkConfig.FPS_THRESHOLDS.FAIR and 
		   memoryMB < BenchmarkConfig.SAFETY.MAX_MEMORY_MB
end

function PerformanceMonitor:shouldEmergencyStop()
	local fps = self:getCurrentFPS()
	local memoryMB = self:getMemoryUsageMB()
	
	return fps < BenchmarkConfig.EMERGENCY_STOP_FPS or
		   memoryMB > BenchmarkConfig.SAFETY.MAX_MEMORY_MB
end

function PerformanceMonitor:reset()
	self.fpsHistory = {}
	self.currentFPS = 60
	self.memoryUsage = 0
	self.ping = 0
end

function PerformanceMonitor:destroy()
	if self.heartbeatConnection then
		self.heartbeatConnection:Disconnect()
		self.heartbeatConnection = nil
	end
	
	if self.sampleConnection then
		self.sampleConnection:Disconnect()
		self.sampleConnection = nil
	end
end

return PerformanceMonitor
