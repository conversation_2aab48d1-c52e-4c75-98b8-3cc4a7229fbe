--[[
	Benchmark UI Module
	
	Handles the creation and management of the benchmark user interface.
	Provides real-time performance display, test controls, and results visualization.
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local HttpService = game:GetService("HttpService")

local BenchmarkConfig = require(script.Parent.BenchmarkConfig)

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

local BenchmarkUI = {}
BenchmarkUI.__index = BenchmarkUI

function BenchmarkUI.new(benchmarkManager)
	local self = setmetatable({}, BenchmarkUI)
	self.benchmarkManager = benchmarkManager
	
	-- Create UI
	self:createUI()
	
	-- Connect update loop
	self.updateConnection = RunService.Heartbeat:Connect(function()
		self:updateUI()
	end)
	
	return self
end

function BenchmarkUI:createUI()
	-- Main ScreenGui
	local screenGui = Instance.new("ScreenGui")
	screenGui.Name = "FPSBenchmarkUI"
	screenGui.ResetOnSpawn = false
	screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	screenGui.Parent = playerGui
	self.screenGui = screenGui
	
	-- Main Frame
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(0, BenchmarkConfig.UI.MAIN_FRAME_SIZE[1], 0, BenchmarkConfig.UI.MAIN_FRAME_SIZE[2])
	mainFrame.Position = UDim2.new(0.5, -BenchmarkConfig.UI.MAIN_FRAME_SIZE[1]/2, 0.5, -BenchmarkConfig.UI.MAIN_FRAME_SIZE[2]/2)
	mainFrame.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BACKGROUND
	mainFrame.BorderSizePixel = 0
	mainFrame.Parent = screenGui
	self.mainFrame = mainFrame
	
	-- Add UICorner
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, BenchmarkConfig.UI.CORNER_RADIUS)
	corner.Parent = mainFrame
	
	-- Add UIStroke
	local stroke = Instance.new("UIStroke")
	stroke.Color = BenchmarkConfig.UI.COLORS.BORDER
	stroke.Thickness = BenchmarkConfig.UI.STROKE_THICKNESS
	stroke.Parent = mainFrame
	
	-- Title
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Name = "TitleLabel"
	titleLabel.Size = UDim2.new(1, 0, 0, 50)
	titleLabel.Position = UDim2.new(0, 0, 0, 0)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Font = Enum.Font.GothamBold
	titleLabel.Text = "Roblox FPS Benchmark"
	titleLabel.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	titleLabel.TextSize = 24
	titleLabel.Parent = mainFrame
	
	-- Performance Display Frame
	self:createPerformanceDisplay()
	
	-- Test Controls Frame
	self:createTestControls()
	
	-- Control Buttons Frame
	self:createControlButtons()
end

function BenchmarkUI:createPerformanceDisplay()
	local performanceFrame = Instance.new("Frame")
	performanceFrame.Name = "PerformanceFrame"
	performanceFrame.Size = UDim2.new(1, -40, 0, 120)
	performanceFrame.Position = UDim2.new(0, 20, 0, 60)
	performanceFrame.BackgroundColor3 = BenchmarkConfig.UI.COLORS.SECONDARY_BACKGROUND
	performanceFrame.BorderSizePixel = 0
	performanceFrame.Parent = self.mainFrame
	self.performanceFrame = performanceFrame
	
	-- Add UICorner
	local perfCorner = Instance.new("UICorner")
	perfCorner.CornerRadius = UDim.new(0, 8)
	perfCorner.Parent = performanceFrame
	
	-- FPS Display
	local fpsLabel = Instance.new("TextLabel")
	fpsLabel.Name = "FPSLabel"
	fpsLabel.Size = UDim2.new(0.5, -10, 0, 30)
	fpsLabel.Position = UDim2.new(0, 10, 0, 10)
	fpsLabel.BackgroundTransparency = 1
	fpsLabel.Font = Enum.Font.Gotham
	fpsLabel.Text = "FPS: 60.0"
	fpsLabel.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	fpsLabel.TextSize = 18
	fpsLabel.TextXAlignment = Enum.TextXAlignment.Left
	fpsLabel.Parent = performanceFrame
	self.fpsLabel = fpsLabel
	
	-- Memory Display
	local memoryLabel = Instance.new("TextLabel")
	memoryLabel.Name = "MemoryLabel"
	memoryLabel.Size = UDim2.new(0.5, -10, 0, 30)
	memoryLabel.Position = UDim2.new(0.5, 0, 0, 10)
	memoryLabel.BackgroundTransparency = 1
	memoryLabel.Font = Enum.Font.Gotham
	memoryLabel.Text = "Memory: 0 MB"
	memoryLabel.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	memoryLabel.TextSize = 18
	memoryLabel.TextXAlignment = Enum.TextXAlignment.Left
	memoryLabel.Parent = performanceFrame
	self.memoryLabel = memoryLabel
	
	-- Performance Rating
	local ratingLabel = Instance.new("TextLabel")
	ratingLabel.Name = "RatingLabel"
	ratingLabel.Size = UDim2.new(1, -20, 0, 30)
	ratingLabel.Position = UDim2.new(0, 10, 0, 45)
	ratingLabel.BackgroundTransparency = 1
	ratingLabel.Font = Enum.Font.GothamBold
	ratingLabel.Text = "Performance: Excellent"
	ratingLabel.TextColor3 = BenchmarkConfig.UI.COLORS.EXCELLENT
	ratingLabel.TextSize = 16
	ratingLabel.TextXAlignment = Enum.TextXAlignment.Left
	ratingLabel.Parent = performanceFrame
	self.ratingLabel = ratingLabel
	
	-- Objects Count
	local objectsLabel = Instance.new("TextLabel")
	objectsLabel.Name = "ObjectsLabel"
	objectsLabel.Size = UDim2.new(1, -20, 0, 30)
	objectsLabel.Position = UDim2.new(0, 10, 0, 75)
	objectsLabel.BackgroundTransparency = 1
	objectsLabel.Font = Enum.Font.Gotham
	objectsLabel.Text = "Objects: 0"
	objectsLabel.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	objectsLabel.TextSize = 16
	objectsLabel.TextXAlignment = Enum.TextXAlignment.Left
	objectsLabel.Parent = performanceFrame
	self.objectsLabel = objectsLabel
end

function BenchmarkUI:createTestControls()
	local controlsFrame = Instance.new("Frame")
	controlsFrame.Name = "ControlsFrame"
	controlsFrame.Size = UDim2.new(1, -40, 0, 200)
	controlsFrame.Position = UDim2.new(0, 20, 0, 200)
	controlsFrame.BackgroundColor3 = BenchmarkConfig.UI.COLORS.SECONDARY_BACKGROUND
	controlsFrame.BorderSizePixel = 0
	controlsFrame.Parent = self.mainFrame
	self.controlsFrame = controlsFrame
	
	-- Add UICorner
	local controlsCorner = Instance.new("UICorner")
	controlsCorner.CornerRadius = UDim.new(0, 8)
	controlsCorner.Parent = controlsFrame
	
	-- Test Buttons
	local testNames = {"geometry", "lighting", "physics", "rendering"}
	local testDisplayNames = {"Geometry Test", "Lighting Test", "Physics Test", "Rendering Test"}
	
	self.testButtons = {}
	for i, testName in ipairs(testNames) do
		local button = Instance.new("TextButton")
		button.Name = testName .. "Button"
		button.Size = UDim2.new(0.45, 0, 0, 35)
		button.Position = UDim2.new(
			((i - 1) % 2) * 0.5 + 0.05,
			0,
			math.floor((i - 1) / 2) * 0.25 + 0.1,
			0
		)
		button.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_PRIMARY
		button.BorderSizePixel = 0
		button.Font = Enum.Font.Gotham
		button.Text = testDisplayNames[i]
		button.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
		button.TextSize = 14
		button.Parent = controlsFrame
		
		-- Add UICorner
		local buttonCorner = Instance.new("UICorner")
		buttonCorner.CornerRadius = UDim.new(0, 6)
		buttonCorner.Parent = button
		
		-- Connect button
		button.MouseButton1Click:Connect(function()
			self:startTest(testName)
		end)
		
		self.testButtons[testName] = button
	end
end

function BenchmarkUI:createControlButtons()
	local buttonsFrame = Instance.new("Frame")
	buttonsFrame.Name = "ButtonsFrame"
	buttonsFrame.Size = UDim2.new(1, -20, 0, 40)
	buttonsFrame.Position = UDim2.new(0, 10, 0, 150)
	buttonsFrame.BackgroundTransparency = 1
	buttonsFrame.Parent = self.controlsFrame
	
	-- Stop Button
	local stopButton = Instance.new("TextButton")
	stopButton.Name = "StopButton"
	stopButton.Size = UDim2.new(0.3, -5, 1, 0)
	stopButton.Position = UDim2.new(0, 0, 0, 0)
	stopButton.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_DANGER
	stopButton.BorderSizePixel = 0
	stopButton.Font = Enum.Font.Gotham
	stopButton.Text = "Stop"
	stopButton.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	stopButton.TextSize = 14
	stopButton.Parent = buttonsFrame
	self.stopButton = stopButton
	
	-- Add UICorner
	local stopCorner = Instance.new("UICorner")
	stopCorner.CornerRadius = UDim.new(0, 6)
	stopCorner.Parent = stopButton
	
	-- Reset Button
	local resetButton = Instance.new("TextButton")
	resetButton.Name = "ResetButton"
	resetButton.Size = UDim2.new(0.3, -5, 1, 0)
	resetButton.Position = UDim2.new(0.35, 0, 0, 0)
	resetButton.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_WARNING
	resetButton.BorderSizePixel = 0
	resetButton.Font = Enum.Font.Gotham
	resetButton.Text = "Reset"
	resetButton.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	resetButton.TextSize = 14
	resetButton.Parent = buttonsFrame
	self.resetButton = resetButton
	
	-- Add UICorner
	local resetCorner = Instance.new("UICorner")
	resetCorner.CornerRadius = UDim.new(0, 6)
	resetCorner.Parent = resetButton
	
	-- Results Button
	local resultsButton = Instance.new("TextButton")
	resultsButton.Name = "ResultsButton"
	resultsButton.Size = UDim2.new(0.3, -5, 1, 0)
	resultsButton.Position = UDim2.new(0.7, 0, 0, 0)
	resultsButton.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_SECONDARY
	resultsButton.BorderSizePixel = 0
	resultsButton.Font = Enum.Font.Gotham
	resultsButton.Text = "Results"
	resultsButton.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	resultsButton.TextSize = 14
	resultsButton.Parent = buttonsFrame
	self.resultsButton = resultsButton
	
	-- Add UICorner
	local resultsCorner = Instance.new("UICorner")
	resultsCorner.CornerRadius = UDim.new(0, 6)
	resultsCorner.Parent = resultsButton
	
	-- Connect control buttons
	stopButton.MouseButton1Click:Connect(function()
		self.benchmarkManager:stopBenchmark()
	end)
	
	resetButton.MouseButton1Click:Connect(function()
		self.benchmarkManager:resetBenchmark()
	end)
	
	resultsButton.MouseButton1Click:Connect(function()
		self:showResults()
	end)
end

function BenchmarkUI:startTest(testName)
	local success, message = self.benchmarkManager:startBenchmark(testName)
	if success then
		-- Update button states
		for name, button in pairs(self.testButtons) do
			if name == testName then
				button.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_SUCCESS
				button.Text = button.Text .. " (Running)"
			else
				button.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_DISABLED
			end
		end
	else
		-- Show error message
		warn("Failed to start test:", message)
	end
end

function BenchmarkUI:updateUI()
	-- Update performance data
	local perfData = self.benchmarkManager:getPerformanceData()

	self.fpsLabel.Text = string.format("FPS: %.1f", perfData.fps)
	self.memoryLabel.Text = string.format("Memory: %.1f MB", perfData.memory / 1024 / 1024)

	local rating, color = perfData.rating()
	self.ratingLabel.Text = "Performance: " .. rating
	self.ratingLabel.TextColor3 = color

	-- Update current test info
	local testInfo = self.benchmarkManager:getCurrentTestInfo()
	if testInfo then
		self.objectsLabel.Text = string.format("Objects: %d (%.1f%%)",
			testInfo.objectCount, testInfo.progress * 100)
	else
		self.objectsLabel.Text = "Objects: 0"
	end

	-- Update button states if not running
	if not self.benchmarkManager.isRunning then
		for name, button in pairs(self.testButtons) do
			button.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_PRIMARY
			local displayNames = {
				geometry = "Geometry Test",
				lighting = "Lighting Test",
				physics = "Physics Test",
				rendering = "Rendering Test"
			}
			button.Text = displayNames[name]
		end
	end
end

function BenchmarkUI:showResults()
	local results = self.benchmarkManager:getResults()

	-- Create results window
	local resultsGui = Instance.new("ScreenGui")
	resultsGui.Name = "BenchmarkResults"
	resultsGui.Parent = playerGui

	local resultsFrame = Instance.new("Frame")
	resultsFrame.Size = UDim2.new(0, 500, 0, 400)
	resultsFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
	resultsFrame.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BACKGROUND
	resultsFrame.BorderSizePixel = 0
	resultsFrame.Parent = resultsGui

	-- Add UICorner
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, BenchmarkConfig.UI.CORNER_RADIUS)
	corner.Parent = resultsFrame

	-- Add UIStroke
	local stroke = Instance.new("UIStroke")
	stroke.Color = BenchmarkConfig.UI.COLORS.BORDER
	stroke.Thickness = BenchmarkConfig.UI.STROKE_THICKNESS
	stroke.Parent = resultsFrame

	-- Title
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0, 50)
	title.BackgroundTransparency = 1
	title.Font = Enum.Font.GothamBold
	title.Text = "Benchmark Results"
	title.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	title.TextSize = 24
	title.Parent = resultsFrame

	-- Device info
	local deviceLabel = Instance.new("TextLabel")
	deviceLabel.Size = UDim2.new(1, -20, 0, 30)
	deviceLabel.Position = UDim2.new(0, 10, 0, 50)
	deviceLabel.BackgroundTransparency = 1
	deviceLabel.Font = Enum.Font.Gotham
	deviceLabel.Text = "Device: " .. self.benchmarkManager.deviceInfo.platform
	deviceLabel.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_SECONDARY
	deviceLabel.TextSize = 14
	deviceLabel.TextXAlignment = Enum.TextXAlignment.Left
	deviceLabel.Parent = resultsFrame

	-- Results content
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Size = UDim2.new(1, -20, 1, -140)
	scrollFrame.Position = UDim2.new(0, 10, 0, 90)
	scrollFrame.BackgroundTransparency = 1
	scrollFrame.BorderSizePixel = 0
	scrollFrame.ScrollBarThickness = 8
	scrollFrame.Parent = resultsFrame

	-- Add results text
	local yPos = 0
	for testName, result in pairs(results) do
		if result.completed then
			local testLabel = Instance.new("TextLabel")
			testLabel.Size = UDim2.new(1, 0, 0, 80)
			testLabel.Position = UDim2.new(0, 0, 0, yPos)
			testLabel.BackgroundTransparency = 1
			testLabel.Font = Enum.Font.Gotham
			testLabel.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
			testLabel.TextSize = 14
			testLabel.TextXAlignment = Enum.TextXAlignment.Left
			testLabel.TextYAlignment = Enum.TextYAlignment.Top
			testLabel.Text = string.format(
				"%s Test:\n  • Objects at 60 FPS: %d\n  • Objects at 30 FPS: %d\n  • Max Objects: %d",
				testName:gsub("^%l", string.upper),
				result.objectsAt60FPS,
				result.objectsAt30FPS,
				result.maxObjects
			)
			testLabel.Parent = scrollFrame
			yPos = yPos + 90
		end
	end

	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 100, 0, 30)
	closeButton.Position = UDim2.new(0.5, -50, 1, -40)
	closeButton.BackgroundColor3 = BenchmarkConfig.UI.COLORS.BUTTON_DANGER
	closeButton.BorderSizePixel = 0
	closeButton.Font = Enum.Font.Gotham
	closeButton.Text = "Close"
	closeButton.TextColor3 = BenchmarkConfig.UI.COLORS.TEXT_PRIMARY
	closeButton.TextSize = 14
	closeButton.Parent = resultsFrame

	-- Add UICorner
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton

	closeButton.MouseButton1Click:Connect(function()
		resultsGui:Destroy()
	end)
end

function BenchmarkUI:destroy()
	if self.updateConnection then
		self.updateConnection:Disconnect()
	end

	if self.screenGui then
		self.screenGui:Destroy()
	end
end

return BenchmarkUI
